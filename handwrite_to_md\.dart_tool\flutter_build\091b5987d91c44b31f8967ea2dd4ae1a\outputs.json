["D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "D:\\android\\handwritetomd\\handwrite_to_md\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]