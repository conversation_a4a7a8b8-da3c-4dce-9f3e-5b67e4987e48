import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../services/demo_data_service.dart';

class DemoResult {
  final int id;
  final String title;
  final String filename;
  final String content;
  final DateTime processedAt;
  String backupStatus;

  DemoResult({
    required this.id,
    required this.title,
    required this.filename,
    required this.content,
    required this.processedAt,
    required this.backupStatus,
  });
}

class DemoResultsPage extends StatefulWidget {
  const DemoResultsPage({super.key});

  @override
  State<DemoResultsPage> createState() => _DemoResultsPageState();
}

class _DemoResultsPageState extends State<DemoResultsPage> {
  List<DemoResult> _results = [];
  bool _isLoading = false;
  DemoResult? _selectedResult;

  @override
  void initState() {
    super.initState();
    _loadResults();
  }

  Future<void> _loadResults() async {
    setState(() => _isLoading = true);

    // 获取已删除的项目ID列表
    final deletedIds = await DemoDataService.getDeletedItemIds();

    // 加载演示数据
    final allResults = [
    DemoResult(
      id: 1,
      title: '学习笔记',
      filename: 'flutter_notes_${DateFormat('yyyyMMdd_HHmm').format(DateTime.now())}.md',
      content: '''# Flutter学习笔记

## Widget基础

### StatelessWidget
- 不可变的widget
- 适用于静态内容
- 继承StatelessWidget类

### StatefulWidget
- 可变的widget
- 有状态管理
- 继承StatefulWidget类

## 布局Widget

### Column
- 垂直布局
- mainAxisAlignment控制主轴对齐
- crossAxisAlignment控制交叉轴对齐

### Row
- 水平布局
- 与Column类似的属性

### Container
- 通用容器widget
- 可设置padding、margin、decoration等

## 常用属性
- padding: 内边距
- margin: 外边距
- decoration: 装饰（背景色、边框等）
- alignment: 对齐方式''',
      processedAt: DateTime.now().subtract(const Duration(days: 1)),
      backupStatus: 'none',
    ),
    DemoResult(
      id: 2,
      title: '购物清单',
      filename: 'shopping_list_${DateFormat('yyyyMMdd_HHmm').format(DateTime.now().subtract(const Duration(days: 2)))}.md',
      content: '''# 购物清单

## 生活用品
- [x] 洗发水
- [x] 牙膏
- [ ] 毛巾
- [ ] 洗衣液

## 食品
- [x] 大米
- [x] 面条
- [ ] 鸡蛋
- [ ] 牛奶
- [ ] 苹果
- [ ] 香蕉

## 办公用品
- [ ] 笔记本
- [ ] 圆珠笔
- [ ] 便利贴

## 预算
总预算：500元
已花费：120元
剩余：380元''',
      processedAt: DateTime.now().subtract(const Duration(days: 2)),
      backupStatus: 'failed',
    ),
  ];

  // 过滤掉已删除的项目
  _results = allResults.where((result) => !deletedIds.contains(result.id)).toList();

  setState(() => _isLoading = false);
}

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('处理结果'),
        actions: [
          IconButton(
            icon: const Icon(Icons.cloud_upload),
            onPressed: _showBackupDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadResults,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _results.isEmpty
              ? const Center(
                  child: Text(
                    '暂无处理结果\n所有演示数据已被删除',
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.grey),
                  ),
                )
              : ListView.builder(
        itemCount: _results.length,
        itemBuilder: (context, index) {
          final result = _results[index];
          final isSelected = _selectedResult?.id == result.id;

          return Card(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : null,
            child: ListTile(
              leading: const Icon(Icons.description),
              title: isSelected
                  ? SizedBox(
                      height: 20,
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Text(result.title),
                      ),
                    )
                  : Text(
                      result.title,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  isSelected
                      ? SizedBox(
                          height: 16,
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Text(
                              '${DateFormat('yyyy-MM-dd HH:mm').format(result.processedAt)} • ${result.filename}',
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                        )
                      : Text(
                          '${DateFormat('yyyy-MM-dd HH:mm').format(result.processedAt)} • ${result.filename}',
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        result.backupStatus == 'success' 
                            ? Icons.cloud_done 
                            : result.backupStatus == 'failed'
                                ? Icons.cloud_off
                                : Icons.cloud_queue,
                        size: 16,
                        color: result.backupStatus == 'success' 
                            ? Colors.green 
                            : result.backupStatus == 'failed'
                                ? Colors.red
                                : Colors.grey,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        result.backupStatus == 'success' 
                            ? '已备份' 
                            : result.backupStatus == 'failed'
                                ? '备份失败'
                                : '未备份',
                        style: TextStyle(
                          fontSize: 12,
                          color: result.backupStatus == 'success' 
                              ? Colors.green 
                              : result.backupStatus == 'failed'
                                  ? Colors.red
                                  : Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(Icons.preview),
                    onPressed: () => _previewResult(result),
                  ),
                  IconButton(
                    icon: const Icon(Icons.cloud_upload),
                    onPressed: () => _backupSingleFile(result),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleMenuAction(value, result),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'share',
                        child: Row(
                          children: [
                            Icon(Icons.share),
                            SizedBox(width: 8),
                            Text('分享'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red),
                            SizedBox(width: 8),
                            Text('删除', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              onTap: () => _selectResult(result),
            ),
          );
        },
      ),
    );
  }

  void _selectResult(DemoResult result) {
    setState(() {
      _selectedResult = _selectedResult?.id == result.id ? null : result;
    });
  }

  void _previewResult(DemoResult result) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DemoPreviewPage(result: result),
      ),
    );
  }

  void _backupSingleFile(DemoResult result) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在备份文件...'),
          ],
        ),
      ),
    );

    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        Navigator.of(context).pop();
        setState(() {
          result.backupStatus = 'success';
        });
        _showSuccessSnackBar('文件备份成功（演示）');
      }
    });
  }

  void _showBackupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('备份到云端'),
        content: const Text('确定要将所有文件备份到云端吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _backupAllFiles();
            },
            child: const Text('备份'),
          ),
        ],
      ),
    );
  }

  void _backupAllFiles() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在批量备份文件...'),
          ],
        ),
      ),
    );

    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        Navigator.of(context).pop();
        setState(() {
          for (var result in _results) {
            result.backupStatus = 'success';
          }
        });
        _showSuccessSnackBar('批量备份完成（演示）');
      }
    });
  }

  void _refreshResults() {
    _showInfoSnackBar('结果已刷新（演示）');
  }

  void _handleMenuAction(String action, DemoResult result) {
    switch (action) {
      case 'share':
        _showInfoSnackBar('分享功能开发中');
        break;
      case 'delete':
        _deleteResult(result);
        break;
    }
  }

  Future<void> _deleteResult(DemoResult result) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除确认'),
        content: Text('确定要删除"${result.title}"吗？此操作不可撤销。\n\n注意：如果存在对应的实际文件，也会被删除。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // 显示删除进度
        _showLoadingDialog('正在删除...');

        // 标记为已删除（持久化）
        await DemoDataService.markItemAsDeleted(result.id);

        // 尝试删除对应的实际文件
        final fileDeleted = await DemoDataService.tryDeleteDemoFile(result.filename);

        if (mounted) {
          Navigator.of(context).pop(); // 关闭进度对话框
        }

        // 从当前列表中移除
        setState(() {
          _results.remove(result);
        });

        // 显示删除结果
        if (fileDeleted) {
          _showSuccessSnackBar('删除成功（包括对应文件）');
        } else {
          _showSuccessSnackBar('删除成功（未找到对应文件）');
        }
      } catch (e) {
        if (mounted) {
          Navigator.of(context).pop(); // 关闭进度对话框
        }
        _showErrorSnackBar('删除失败: $e');
      }
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(message),
          ],
        ),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}

class DemoPreviewPage extends StatelessWidget {
  final DemoResult result;

  const DemoPreviewPage({super.key, required this.result});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(result.title),
        actions: [
          IconButton(
            icon: const Icon(Icons.copy),
            onPressed: () => _copyToClipboard(context),
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareContent(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.info_outline),
                        const SizedBox(width: 8),
                        Text(
                          '文件信息',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text('文件名: ${result.filename}'),
                    Text('处理时间: ${DateFormat('yyyy-MM-dd HH:mm:ss').format(result.processedAt)}'),
                    Text('字符数: ${result.content.length}'),
                    Text('行数: ${result.content.split('\n').length}'),
                    Row(
                      children: [
                        const Text('备份状态: '),
                        Icon(
                          result.backupStatus == 'success'
                              ? Icons.cloud_done
                              : result.backupStatus == 'failed'
                                  ? Icons.cloud_off
                                  : Icons.cloud_queue,
                          size: 16,
                          color: result.backupStatus == 'success'
                              ? Colors.green
                              : result.backupStatus == 'failed'
                                  ? Colors.red
                                  : Colors.grey,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          result.backupStatus == 'success'
                              ? '已备份'
                              : result.backupStatus == 'failed'
                                  ? '备份失败'
                                  : '未备份',
                          style: TextStyle(
                            color: result.backupStatus == 'success'
                                ? Colors.green
                                : result.backupStatus == 'failed'
                                    ? Colors.red
                                    : Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.description),
                        const SizedBox(width: 8),
                        Text(
                          'Markdown内容',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        result.content,
                        style: const TextStyle(
                          fontFamily: 'monospace',
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _copyToClipboard(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('内容已复制到剪贴板（演示）')),
    );
  }

  void _shareContent(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('分享功能开发中'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}
