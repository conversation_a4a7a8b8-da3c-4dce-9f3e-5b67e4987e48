class MarkdownFile {
  final int? id;
  final String filename;
  final String content;
  final String filePath;
  final DateTime createdAt;
  final String? backupStatus;
  final DateTime? backupTime;

  MarkdownFile({
    this.id,
    required this.filename,
    required this.content,
    required this.filePath,
    required this.createdAt,
    this.backupStatus,
    this.backupTime,
  });

  MarkdownFile copyWith({
    int? id,
    String? filename,
    String? content,
    String? filePath,
    DateTime? createdAt,
    String? backupStatus,
    DateTime? backupTime,
  }) {
    return MarkdownFile(
      id: id ?? this.id,
      filename: filename ?? this.filename,
      content: content ?? this.content,
      filePath: filePath ?? this.filePath,
      createdAt: createdAt ?? this.createdAt,
      backupStatus: backupStatus ?? this.backupStatus,
      backupTime: backupTime ?? this.backupTime,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'filename': filename,
      'content': content,
      'filePath': filePath,
      'createdAt': createdAt.toIso8601String(),
      'backupStatus': backupStatus,
      'backupTime': backupTime?.toIso8601String(),
    };
  }

  factory MarkdownFile.fromJson(Map<String, dynamic> json) {
    return MarkdownFile(
      id: json['id'],
      filename: json['filename'],
      content: json['content'],
      filePath: json['filePath'],
      createdAt: DateTime.parse(json['createdAt']),
      backupStatus: json['backupStatus'],
      backupTime: json['backupTime'] != null 
          ? DateTime.parse(json['backupTime']) 
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'filename': filename,
      'content': content,
      'file_path': filePath,
      'created_at': createdAt.toIso8601String(),
      'backup_status': backupStatus ?? 'none',
      'backup_time': backupTime?.toIso8601String(),
    };
  }

  factory MarkdownFile.fromMap(Map<String, dynamic> map) {
    return MarkdownFile(
      id: map['id'],
      filename: map['filename'],
      content: map['content'],
      filePath: map['file_path'],
      createdAt: DateTime.parse(map['created_at']),
      backupStatus: map['backup_status'],
      backupTime: map['backup_time'] != null 
          ? DateTime.parse(map['backup_time']) 
          : null,
    );
  }
}
