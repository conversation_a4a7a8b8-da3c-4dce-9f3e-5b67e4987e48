import 'dart:io';

class ArticleGroup {
  final String id;
  final List<ImageItem> images;
  final String title;

  ArticleGroup({
    required this.id,
    required this.images,
    String? title,
  }) : title = title ?? '文章 $id';

  ArticleGroup copyWith({
    String? id,
    List<ImageItem>? images,
    String? title,
  }) {
    return ArticleGroup(
      id: id ?? this.id,
      images: images ?? this.images,
      title: title ?? this.title,
    );
  }

  // 获取排序后的图片名称
  List<String> getImageNames() {
    return images.asMap().entries.map((entry) {
      final index = entry.key;
      return '图片${index + 1}';
    }).toList();
  }

  // 检查是否为多图片文章
  bool get isMultiImage => images.length > 1;

  // 获取第一张图片作为预览
  ImageItem? get previewImage => images.isNotEmpty ? images.first : null;
}

class ImageItem {
  final String id;
  final File file;
  final String name;
  final DateTime createdAt;

  ImageItem({
    required this.id,
    required this.file,
    String? name,
    DateTime? createdAt,
  }) : name = name ?? file.path.split('/').last,
        createdAt = createdAt ?? DateTime.now();

  ImageItem copyWith({
    String? id,
    File? file,
    String? name,
    DateTime? createdAt,
  }) {
    return ImageItem(
      id: id ?? this.id,
      file: file ?? this.file,
      name: name ?? this.name,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  String get fileName => file.path.split('/').last;
  String get filePath => file.path;
}
