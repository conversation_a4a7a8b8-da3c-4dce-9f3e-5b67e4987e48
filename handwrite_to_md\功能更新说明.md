# 功能更新说明

## 新增功能概述

本次更新为项目增加了两个重要功能：

### 1. 历史结果页面批量删除功能

#### 功能描述
- 在历史结果页面的顶部增加了"删除所有"按钮
- 支持一键删除所有处理结果，包括数据库记录、Markdown文件和图片文件
- 每个单独的结果仍保留原有的删除按钮

#### 实现细节
- **UI更新**: 在 `results_page.dart` 的 AppBar 中添加了删除所有按钮（垃圾桶图标）
- **数据库操作**: 在 `DatabaseService` 中新增 `deleteAllResults()` 方法
- **文件操作**: 在 `FileService` 中新增 `deleteFiles()` 批量删除方法
- **用户体验**: 
  - 删除前显示确认对话框，详细说明将要删除的内容
  - 删除过程中显示进度对话框
  - 删除完成后显示详细的删除结果统计

#### 删除范围
- 数据库中的所有处理结果记录
- 对应的 Markdown 文件
- 对应的图片文件

### 2. LLM服务多图片输入支持

#### 功能描述
- LLM服务现在支持同时处理多张图片
- 在主页面和工作页面都增加了"选择多张图片"按钮
- 支持 OpenAI 和 Claude 两种 API 的多图片处理

#### 实现细节

##### LLM服务更新
- **新方法**: `processImages(List<File> imageFiles, AppSettings settings)`
- **向后兼容**: 原有的 `processImage()` 方法仍然可用，内部调用新的多图片方法
- **API适配**: 
  - OpenAI API: 支持在 content 数组中添加多个 image_url 对象，每个包含 name 字段
  - Claude API: 支持在 content 数组中添加多个 image 对象，每个包含 name 字段

##### 图片命名规则
- 单张图片: 使用原文件名
- 多张图片: 自动命名为 "图片1 (filename1)", "图片2 (filename2)" 等

##### UI更新
- **主页面** (`home_page.dart`): 新增"选择多张图片"按钮
- **工作页面** (`working_home_page.dart`): 新增"选择多张图片"按钮
- **文件选择**: 使用 FilePicker 支持多选图片文件
- **处理进度**: 显示正在处理的图片数量

##### Mock服务支持
- `MockLLMService` 也更新以支持多图片处理
- 多图片时模拟更长的处理时间
- 提供专门的多图片模拟结果

## 技术实现要点

### 1. 数据库操作
```dart
// 新增批量删除方法
static Future<int> deleteAllResults() async {
  final db = await database;
  return await db.delete(_tableResults);
}
```

### 2. 文件批量删除
```dart
// 新增批量文件删除方法
static Future<Map<String, bool>> deleteFiles(List<String> filePaths) async {
  Map<String, bool> results = {};
  for (String filePath in filePaths) {
    // 删除逻辑...
  }
  return results;
}
```

### 3. LLM API 多图片支持
```dart
// OpenAI API 多图片内容构建
List<Map<String, dynamic>> content = [
  {'type': 'text', 'text': '请识别这些图片...'},
  {
    'type': 'image_url',
    'name': '图片1',
    'image_url': {'url': 'data:image/jpeg;base64,...'}
  },
  // 更多图片...
];
```

### 4. 用户界面改进
- 删除确认对话框显示详细信息
- 进度对话框显示当前操作状态
- 结果统计显示删除成功的文件数量
- 多图片处理时显示图片数量

## 兼容性说明

### 向后兼容
- 所有原有功能保持不变
- 原有的单图片处理方法仍然可用
- 数据库结构无变化
- 设置和配置无变化

### API兼容性
- 支持 OpenAI GPT-4 Vision 系列模型
- 支持 Claude 3 系列模型
- 自动根据模型类型选择合适的API格式

## 使用说明

### 批量删除功能
1. 进入历史结果页面
2. 点击顶部的删除所有按钮（垃圾桶图标）
3. 确认删除操作
4. 等待删除完成并查看结果统计

### 多图片处理功能
1. 在主页面或工作页面点击"选择多张图片"按钮
2. 从文件管理器中选择多张图片（支持常见图片格式）
3. 等待LLM处理所有图片
4. 查看合并后的识别结果
5. 保存结果到数据库和文件系统

## 注意事项

1. **删除操作不可逆**: 批量删除操作会永久删除文件，请谨慎使用
2. **多图片处理时间**: 处理多张图片需要更长时间，请耐心等待
3. **API费用**: 多图片处理会消耗更多API调用费用
4. **文件大小限制**: 请确保图片文件大小符合API提供商的限制
5. **网络要求**: 多图片处理需要稳定的网络连接

## 测试建议

1. 测试单张图片处理功能是否正常
2. 测试多张图片选择和处理功能
3. 测试批量删除功能的各种场景
4. 验证删除操作的完整性
5. 测试错误处理和用户提示
