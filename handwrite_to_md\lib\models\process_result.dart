class ProcessResult {
  final int? id;
  final String title;
  final String filePath;
  final String imagePath;
  final String content;
  final DateTime processedAt;
  final String? backupStatus;
  final DateTime? backupTime;

  ProcessResult({
    this.id,
    required this.title,
    required this.filePath,
    required this.imagePath,
    required this.content,
    required this.processedAt,
    this.backupStatus,
    this.backupTime,
  });

  ProcessResult copyWith({
    int? id,
    String? title,
    String? filePath,
    String? imagePath,
    String? content,
    DateTime? processedAt,
    String? backupStatus,
    DateTime? backupTime,
  }) {
    return ProcessResult(
      id: id ?? this.id,
      title: title ?? this.title,
      filePath: filePath ?? this.filePath,
      imagePath: imagePath ?? this.imagePath,
      content: content ?? this.content,
      processedAt: processedAt ?? this.processedAt,
      backupStatus: backupStatus ?? this.backupStatus,
      backupTime: backupTime ?? this.backupTime,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'file_path': filePath,
      'image_path': imagePath,
      'content': content,
      'processed_at': processedAt.toIso8601String(),
      'backup_status': backupStatus ?? 'none',
      'backup_time': backupTime?.toIso8601String(),
    };
  }

  factory ProcessResult.fromMap(Map<String, dynamic> map) {
    return ProcessResult(
      id: map['id'],
      title: map['title'],
      filePath: map['file_path'],
      imagePath: map['image_path'],
      content: map['content'],
      processedAt: DateTime.parse(map['processed_at']),
      backupStatus: map['backup_status'],
      backupTime: map['backup_time'] != null 
          ? DateTime.parse(map['backup_time']) 
          : null,
    );
  }

  String get filename => filePath.split('/').last;
}
