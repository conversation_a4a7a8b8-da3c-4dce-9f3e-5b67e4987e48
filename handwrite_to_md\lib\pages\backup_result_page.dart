import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/backup_result.dart';

class BackupResultPage extends StatelessWidget {
  final List<BackupResult> results;

  const BackupResultPage({super.key, required this.results});

  @override
  Widget build(BuildContext context) {
    final successCount = results.where((r) => r.success).length;
    final failedCount = results.length - successCount;

    return Scaffold(
      appBar: AppBar(
        title: const Text('备份结果'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _retryFailedBackups(context),
          ),
        ],
      ),
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Column(
                      children: [
                        Text(
                          '$successCount',
                          style: const TextStyle(fontSize: 24, color: Colors.green),
                        ),
                        const Text('成功'),
                      ],
                    ),
                    Column(
                      children: [
                        Text(
                          '$failedCount',
                          style: const TextStyle(fontSize: 24, color: Colors.red),
                        ),
                        const Text('失败'),
                      ],
                    ),
                    Column(
                      children: [
                        Text(
                          '${results.length}',
                          style: const TextStyle(fontSize: 24),
                        ),
                        const Text('总计'),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: results.length,
              itemBuilder: (context, index) {
                final result = results[index];
                return ListTile(
                  leading: Icon(
                    result.success ? Icons.check_circle : Icons.error,
                    color: result.success ? Colors.green : Colors.red,
                  ),
                  title: Text(result.filename),
                  subtitle: result.success
                      ? Text(
                          '备份成功 - ${DateFormat('yyyy-MM-dd HH:mm').format(result.timestamp)}')
                      : Text('备份失败: ${result.error}'),
                  trailing: result.success
                      ? null
                      : IconButton(
                          icon: const Icon(Icons.refresh),
                          onPressed: () => _retryBackup(context, result.filename),
                        ),
                );
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: failedCount > 0 ? () => _retryFailedBackups(context) : null,
                child: Text('重试失败项 ($failedCount)'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: OutlinedButton(
                onPressed: () => _exportResults(context),
                child: const Text('导出结果'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _retryBackup(BuildContext context, String filename) {
    // TODO: 实现单个文件重试备份
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('重试备份: $filename')),
    );
  }

  void _retryFailedBackups(BuildContext context) {
    final failedResults = results.where((r) => !r.success).toList();
    if (failedResults.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('没有失败的备份项')),
      );
      return;
    }

    // TODO: 实现批量重试失败的备份
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('重试 ${failedResults.length} 个失败项')),
    );
  }

  void _exportResults(BuildContext context) {
    // TODO: 实现导出备份结果
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('导出备份结果')),
    );
  }
}
