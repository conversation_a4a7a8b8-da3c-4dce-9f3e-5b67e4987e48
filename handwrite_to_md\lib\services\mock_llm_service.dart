import 'dart:io';
import 'dart:math';
import '../models/app_settings.dart';

class MockLLMService {
  final Random _random = Random();

  Future<String> processImage(File imageFile, AppSettings settings) async {
    return await processImages([imageFile], settings);
  }

  Future<String> processImages(List<File> imageFiles, AppSettings settings) async {
    // 模拟网络延迟，多图片时延迟更长
    final baseDelay = 2 + _random.nextInt(3);
    final extraDelay = imageFiles.length > 1 ? imageFiles.length : 0;
    await Future.delayed(Duration(seconds: baseDelay + extraDelay));

    // 模拟可能的错误
    if (_random.nextDouble() < 0.1) { // 10% 概率出错
      throw Exception('网络连接超时，请检查网络设置');
    }

    if (_random.nextDouble() < 0.05) { // 5% 概率API错误
      throw Exception('API调用失败: 401 Unauthorized - 请检查API密钥');
    }

    // 返回模拟的识别结果
    if (imageFiles.length > 1) {
      return _generateMultiImageMockResult(imageFiles.length);
    } else {
      return _generateMockResult();
    }
  }

  String _generateMockResult() {
    final results = [

      '''# 学习笔记 - Flutter开发

## Widget基础概念

### StatelessWidget
- 不可变的widget
- 适用于静态内容展示
- 性能较好，无状态管理开销

```dart
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Text('Hello World'),
    );
  }
}
```

### StatefulWidget
- 可变的widget
- 有内部状态管理
- 适用于动态内容

### 常用布局Widget

#### Column & Row
- **Column**: 垂直布局
- **Row**: 水平布局
- 主要属性：
  - `mainAxisAlignment`: 主轴对齐
  - `crossAxisAlignment`: 交叉轴对齐
  - `children`: 子widget列表

#### Container
- 通用容器widget
- 可设置：
  - `padding`: 内边距
  - `margin`: 外边距
  - `decoration`: 装饰效果
  - `width/height`: 尺寸

## 状态管理

### setState()
- 最基础的状态管理方式
- 适用于简单的局部状态

### Provider
- 推荐的状态管理方案
- 基于InheritedWidget
- 支持依赖注入

## 重要概念
- **BuildContext**: Widget树中的位置信息
- **Key**: Widget的唯一标识
- **生命周期**: initState, build, dispose等

## 最佳实践
1. 合理使用const构造函数
2. 避免在build方法中创建对象
3. 使用ListView.builder处理长列表
4. 及时释放资源（dispose）''',

      '''# 购物清单

## 生活用品
- [x] 洗发水 (已购买)
- [x] 沐浴露 (已购买)
- [ ] 牙膏
- [ ] 毛巾 (2条)
- [ ] 洗衣液
- [ ] 纸巾 (3包)

## 食品类
- [x] 大米 (5kg)
- [x] 食用油
- [ ] 鸡蛋 (30个)
- [ ] 牛奶 (2L装)
- [ ] 面包
- [ ] 苹果 (2kg)
- [ ] 香蕉 (1kg)
- [ ] 青菜
- [ ] 猪肉 (1kg)

## 零食饮料
- [ ] 薯片
- [ ] 饼干
- [ ] 果汁
- [ ] 咖啡

## 日用品
- [ ] 垃圾袋
- [ ] 保鲜膜
- [ ] 洗洁精
- [ ] 拖把

## 预算统计
- **总预算**: ¥500
- **已花费**: ¥120
- **剩余预算**: ¥380

## 购物提醒
1. 周末去超市采购
2. 记得带购物袋
3. 对比价格选择性价比高的商品
4. 检查保质期''',

      '''# 读书笔记 - 《深度工作》

## 核心观点

### 什么是深度工作
> 深度工作是指在无干扰的状态下专注进行职业活动的能力。

### 深度工作的价值
1. **经济价值**: 在知识经济时代更有竞争力
2. **个人成长**: 快速掌握复杂技能
3. **意义感**: 获得更多成就感

## 实践方法

### 1. 深度工作哲学
- **禁欲主义**: 完全隔离干扰
- **双峰模式**: 分配特定时间深度工作
- **节奏模式**: 建立固定的工作节奏
- **新闻记者模式**: 随时切换到深度状态

### 2. 四个准则

#### 准则1: 专注于极端重要的事
- 确定1-2个最重要的目标
- 避免目标过多导致精力分散

#### 准则2: 抓住引领性指标
- 关注过程指标而非结果指标
- 例如：专注工作的小时数

#### 准则3: 保持醒目的计分板
- 可视化进度跟踪
- 定期回顾和调整

#### 准则4: 定期问责
- 每周回顾进展
- 分析成功和失败的原因

## 个人实践计划
1. **时间安排**: 每天早上6:00-8:00深度工作
2. **环境设置**: 关闭手机，清理桌面
3. **目标设定**: 每周完成一个重要项目
4. **进度跟踪**: 使用番茄工作法记录时间

## 关键收获
- 深度工作是一种技能，需要刻意练习
- 浅层工作虽然必要，但要严格控制时间
- 建立仪式感有助于进入深度工作状态''',
    ];

    return results[_random.nextInt(results.length)];
  }

  String _generateMultiImageMockResult(int imageCount) {
    final multiImageResults = [
      '''# 多页笔记整理

## 第一页 - 学习计划
- 每日学习时间：2小时
- 重点科目：数学、英语
- 复习方式：做题+总结

## 第二页 - 今日任务
- [x] 完成数学作业
- [x] 背诵英语单词50个
- [ ] 整理错题本
- [ ] 预习明天课程

## 第三页 - 心得体会
学习需要持之以恒，每天进步一点点，积累起来就是很大的进步。
要保持良好的学习习惯，制定合理的学习计划。''',

      '''# 会议记录汇总

## 会议一：项目启动会
**时间**：2024年1月15日
**参与人员**：张三、李四、王五
**主要议题**：
- 项目目标确定
- 时间节点规划
- 资源分配

## 会议二：进度检查会
**时间**：2024年1月22日
**参与人员**：全体项目组成员
**主要内容**：
- 各模块进度汇报
- 问题讨论与解决
- 下周工作安排

## 会议三：技术评审会
**时间**：2024年1月29日
**讨论要点**：
- 技术方案评审
- 风险评估
- 优化建议''',

      '''# 读书笔记合集

## 《深度工作》- 第1章
**核心观点**：在信息时代，深度工作能力越来越稀缺，也越来越有价值。

**关键要点**：
- 深度工作定义：在无干扰的状态下专注进行认知要求高的活动
- 浅层工作：对认知要求不高，往往在受到干扰的情况下开展

## 《深度工作》- 第2章
**主要内容**：深度工作假设的三个论证
1. 深度工作在经济上越来越有价值
2. 深度工作在当今世界越来越稀少
3. 深度工作的意义不仅在于经济回报

## 《深度工作》- 第3章
**实践方法**：
- 建立深度工作的仪式
- 选择深度工作哲学
- 创造支持深度工作的环境''',
    ];

    return multiImageResults[_random.nextInt(multiImageResults.length)];
  }

  Future<bool> testConnection(AppSettings settings) async {
    // 模拟连接测试
    await Future.delayed(const Duration(seconds: 1));
    
    // 简单的设置验证
    if (settings.llmBaseUrl == null || settings.llmBaseUrl!.isEmpty) {
      return false;
    }
    if (settings.llmApiKey == null || settings.llmApiKey!.isEmpty) {
      return false;
    }
    if (settings.llmModel == null || settings.llmModel!.isEmpty) {
      return false;
    }
    
    // 模拟90%成功率
    return _random.nextDouble() < 0.9;
  }

  void dispose() {
    // 清理资源
  }
}
