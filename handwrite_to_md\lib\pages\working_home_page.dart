import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:file_picker/file_picker.dart';
import '../services/llm_service.dart';
import '../services/file_service.dart';
import '../services/database_service.dart';
import '../services/settings_repository.dart';
import '../services/cloud_backup_service.dart';
import '../models/app_settings.dart';
import '../models/process_result.dart';
import 'settings_page.dart';
import 'results_page.dart';
import 'batch_process_page.dart';

class WorkingHomePage extends StatefulWidget {
  const WorkingHomePage({super.key});

  @override
  State<WorkingHomePage> createState() => _WorkingHomePageState();
}

class _WorkingHomePageState extends State<WorkingHomePage> {
  final LLMService _llmService = LLMService();
  AppSettings? _settings;
  SettingsRepository? _settingsRepository;
  bool _isLoading = false;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() => _isLoading = true);
    try {
      // 初始化SettingsRepository
      _settingsRepository = await SettingsRepository.create();

      // 从SettingsRepository加载设置
      final settings = await _settingsRepository!.loadSettings();

      setState(() => _settings = settings);
    } catch (e) {
      _showErrorSnackBar('加载设置失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('手写识别转Markdown'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () => _viewResults(),
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _openSettings(),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildStatusCard(),
            const SizedBox(height: 24),
            _buildActionButtons(),
            const SizedBox(height: 24),
            _buildInstructions(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    final isConfigured = _settings?.isLLMConfigured ?? false;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isConfigured ? Icons.check_circle : Icons.warning,
                  color: isConfigured ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  isConfigured ? 'LLM已配置' : 'LLM未配置',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (!isConfigured)
              const Text(
                '请先在设置中配置LLM服务，才能使用图片识别功能。',
                style: TextStyle(color: Colors.grey),
              ),
            if (isConfigured && _settings != null)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('模型: ${_settings!.llmModel}'),
                  Text('Base URL: ${_settings!.llmBaseUrl}'),
                  if (_settings!.isBackupConfigured)
                    const Text('云端备份: 已配置', style: TextStyle(color: Colors.green))
                  else
                    const Text('云端备份: 未配置', style: TextStyle(color: Colors.grey)),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    final isConfigured = _settings?.isLLMConfigured ?? false;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ElevatedButton.icon(
          onPressed: isConfigured && !_isProcessing ? _pickImageFromCamera : null,
          icon: _isProcessing
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2)
                )
              : const Icon(Icons.camera_alt),
          label: Text(_isProcessing ? '处理中...' : '拍照识别'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
        ),
        const SizedBox(height: 12),
        ElevatedButton.icon(
          onPressed: isConfigured && !_isProcessing ? _pickImageFromGallery : null,
          icon: const Icon(Icons.photo_library),
          label: const Text('从相册选择'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
        ),
        const SizedBox(height: 12),
        OutlinedButton.icon(
          onPressed: isConfigured && !_isProcessing ? _batchProcess : null,
          icon: const Icon(Icons.auto_awesome_motion),
          label: const Text('批量处理'),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
        ),
        const SizedBox(height: 12),
        OutlinedButton.icon(
          onPressed: isConfigured && !_isProcessing ? _pickMultipleImagesFromFile : null,
          icon: const Icon(Icons.photo_library_outlined),
          label: const Text('选择多张图片'),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
        ),
        const SizedBox(height: 12),
        OutlinedButton.icon(
          onPressed: () => _openSettings(),
          icon: const Icon(Icons.settings),
          label: const Text('配置设置'),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
        ),
        const SizedBox(height: 12),
        OutlinedButton.icon(
          onPressed: () => _viewResults(),
          icon: const Icon(Icons.history),
          label: const Text('查看历史结果'),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
        ),
      ],
    );
  }

  Widget _buildInstructions() {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '使用说明',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 16),
              const Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      InstructionItem(
                        step: '1',
                        title: '配置LLM服务',
                        description: '点击"配置设置"按钮，填写LLM API信息：\n• Base URL (如: https://api.openai.com/v1)\n• API Key\n• 选择模型 (如: gpt-4-vision-preview)\n• 自定义系统提示词',
                      ),
                      InstructionItem(
                        step: '2',
                        title: '选择图片',
                        description: '使用"拍照识别"直接拍摄手写内容，或"从相册选择"已有图片。',
                      ),
                      InstructionItem(
                        step: '3',
                        title: '等待处理',
                        description: '应用会自动调用LLM API识别图片中的手写文字，并转换为Markdown格式。',
                      ),
                      InstructionItem(
                        step: '4',
                        title: '查看结果',
                        description: '识别完成后可以预览、编辑、分享或备份到云端。',
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _pickImageFromCamera() async {
    if (!await _checkPermissions()) return;
    
    final picker = ImagePicker();
    try {
      final image = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );
      if (image != null) {
        await _processImage(File(image.path));
      }
    } catch (e) {
      _showErrorSnackBar('拍照失败: $e');
    }
  }

  Future<void> _pickImageFromGallery() async {
    if (!await _checkPermissions()) return;
    
    final picker = ImagePicker();
    try {
      final image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );
      if (image != null) {
        await _processImage(File(image.path));
      }
    } catch (e) {
      _showErrorSnackBar('选择图片失败: $e');
    }
  }

  Future<void> _pickMultipleImagesFromFile() async {
    if (!await _checkFileAccessPermissions()) return;

    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        List<File> imageFiles = [];
        for (final file in result.files) {
          if (file.path != null) {
            imageFiles.add(File(file.path!));
          }
        }

        if (imageFiles.isNotEmpty) {
          await _processImages(imageFiles);
        }
      }
    } catch (e) {
      _showErrorSnackBar('选择图片失败: $e');
    }
  }

  Future<bool> _checkPermissions() async {
    try {
      // 检查相机权限
      var cameraStatus = await Permission.camera.status;
      if (cameraStatus.isDenied) {
        cameraStatus = await Permission.camera.request();
      }

      // 检查存储权限
      bool hasStoragePermission = await _checkStoragePermissions();

      if (!cameraStatus.isGranted) {
        _showErrorSnackBar('需要相机权限才能拍照');
        return false;
      }

      if (!hasStoragePermission) {
        return false;
      }

      return true;
    } catch (e) {
      // 在某些设备上可能不需要权限或权限检查失败
      return true;
    }
  }

  Future<bool> _checkStoragePermissions() async {
    try {
      // 检查存储权限
      var storageStatus = await Permission.storage.status;
      if (storageStatus.isDenied) {
        storageStatus = await Permission.storage.request();
      }

      // 对于Android 13+，检查照片权限
      var photoStatus = await Permission.photos.status;
      if (photoStatus.isDenied) {
        photoStatus = await Permission.photos.request();
      }

      // 检查管理外部存储权限（Android 11+）
      var manageStorageStatus = await Permission.manageExternalStorage.status;
      if (manageStorageStatus.isDenied) {
        // 显示说明对话框
        final shouldRequest = await _showManageStoragePermissionDialog();
        if (shouldRequest) {
          manageStorageStatus = await Permission.manageExternalStorage.request();
        }
      }

      // 至少需要存储/照片权限之一，或者管理外部存储权限
      bool hasStoragePermission = storageStatus.isGranted ||
                                  photoStatus.isGranted ||
                                  manageStorageStatus.isGranted;

      if (!hasStoragePermission) {
        _showErrorSnackBar('需要存储权限才能访问文件\n请在设置中授予存储权限');
        return false;
      }

      return true;
    } catch (e) {
      // 在某些设备上可能不需要权限或权限检查失败
      return true;
    }
  }

  Future<bool> _showManageStoragePermissionDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('需要文件管理权限'),
        content: const Text(
          '为了访问监控文件夹中的图片，应用需要文件管理权限。\n\n'
          '这将允许应用读取您指定的监控文件夹中的图片文件。\n\n'
          '是否授予权限？',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('授予权限'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  // 专门用于文件访问的权限检查（批量处理和多张图片选择）
  Future<bool> _checkFileAccessPermissions() async {
    try {
      // 首先检查基本的相机和存储权限
      if (!await _checkPermissions()) {
        return false;
      }

      if (Platform.isAndroid) {
        // 根据Android版本采用不同的权限策略
        return await _checkAndroidFilePermissions();
      }

      return true;
    } catch (e) {
      // 在某些设备上可能不需要权限或权限检查失败
      return true;
    }
  }

  Future<bool> _checkAndroidFilePermissions() async {
    try {
      // 对于Android 11+，优先尝试管理外部存储权限
      var manageStorageStatus = await Permission.manageExternalStorage.status;

      // 如果已经有管理外部存储权限，直接返回成功
      if (manageStorageStatus.isGranted) {
        return true;
      }

      // 检查基本存储权限
      var storageStatus = await Permission.storage.status;
      if (storageStatus.isDenied) {
        storageStatus = await Permission.storage.request();
      }

      // 对于Android 13+，检查照片权限
      var photoStatus = await Permission.photos.status;
      if (photoStatus.isDenied) {
        photoStatus = await Permission.photos.request();
      }

      // 对于旧版本Android，尝试请求额外的权限
      if (Platform.isAndroid) {
        await _requestLegacyAndroidPermissions();
      }

      // 如果基本权限都有了，对于较新的Android版本尝试请求管理外部存储权限
      if (storageStatus.isGranted || photoStatus.isGranted) {
        if (manageStorageStatus.isDenied) {
          final shouldRequest = await _showFileAccessPermissionDialog();
          if (shouldRequest) {
            manageStorageStatus = await Permission.manageExternalStorage.request();
          }
        }
      }

      // 检查是否有足够的权限
      bool hasFileAccess = manageStorageStatus.isGranted ||
                          storageStatus.isGranted ||
                          photoStatus.isGranted;

      if (!hasFileAccess) {
        _showFileAccessErrorDialog();
        return false;
      }

      return true;
    } catch (e) {
      // 权限检查失败，但在某些设备上可能不需要权限
      return true;
    }
  }

  // 为旧版本Android请求额外权限
  Future<void> _requestLegacyAndroidPermissions() async {
    try {
      // 请求多个权限以确保兼容性
      final permissions = [
        Permission.storage,
        Permission.photos,
        Permission.manageExternalStorage,
      ];

      // 批量请求权限
      final statuses = await permissions.request();

      // 记录权限状态（用于调试）
      for (final permission in permissions) {
        statuses[permission];
        // 权限状态已记录，可以在需要时添加日志
      }
    } catch (e) {
      // 权限请求失败，但不影响主流程
    }
  }

  Future<bool> _showFileAccessPermissionDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('需要文件访问权限'),
        content: const Text(
          '批量处理和多张图片选择功能需要访问设备存储。\n\n'
          '这将允许应用：\n'
          '• 读取监控文件夹中的图片\n'
          '• 选择多张图片进行处理\n'
          '• 保存处理结果到指定位置\n\n'
          '对于较新的Android版本，建议授予"允许管理所有文件"权限以获得最佳体验。\n'
          '对于较旧的Android版本，基本存储权限即可满足需求。\n\n'
          '是否继续？',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('授予权限'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  void _showFileAccessErrorDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('权限不足'),
        content: const Text(
          '批量处理和多张图片选择功能需要文件访问权限。\n\n'
          '请手动授予权限：\n\n'
          '【Android 11及以上版本】\n'
          '1. 设置 → 应用管理 → 本应用\n'
          '2. 权限 → 文件和媒体\n'
          '3. 选择"允许管理所有文件"\n\n'
          '【Android 10及以下版本】\n'
          '1. 设置 → 应用管理 → 本应用\n'
          '2. 权限 → 存储空间\n'
          '3. 允许访问存储空间\n\n'
          '或者您可以使用单张图片的拍照和相册功能。',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('了解'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _openSettings();
            },
            child: const Text('打开设置'),
          ),
        ],
      ),
    );
  }

  void _showDirectoryPermissionErrorDialog(String directory, String errorMessage) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('目录访问权限不足'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('无法访问监控目录：\n$directory\n'),
              const Text('这通常是由于以下原因：\n'),
              const Text('• 应用缺少文件访问权限'),
              const Text('• 目录路径包含特殊字符（如中文）'),
              const Text('• 目录被系统保护'),
              const Text('• 旧版本Android的权限限制\n'),
              const Text('建议解决方案：\n'),
              const Text('1. 授予"管理所有文件"权限：'),
              const Text('   设置 → 应用 → 本应用 → 权限'),
              const Text('2. 使用推荐的公共目录：'),
              const Text('   • /storage/emulated/0/Download'),
              const Text('   • /storage/emulated/0/Pictures'),
              const Text('   • /storage/emulated/0/DCIM'),
              const Text('3. 避免使用中文路径名'),
              const Text('4. 重新选择监控文件夹\n'),
              const Divider(),
              const Text('错误详情：', style: TextStyle(fontWeight: FontWeight.bold)),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  errorMessage,
                  style: const TextStyle(
                    fontSize: 12,
                    fontFamily: 'monospace',
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('了解'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _openSettings();
            },
            child: const Text('重新配置'),
          ),
        ],
      ),
    );
  }

  Future<void> _processImage(File imageFile) async {
    await _processImages([imageFile]);
  }

  Future<void> _processImages(List<File> imageFiles) async {
    if (_settings == null || !_settings!.isLLMConfigured) {
      _showErrorSnackBar('请先配置LLM设置');
      return;
    }

    if (imageFiles.isEmpty) {
      _showErrorSnackBar('没有选择图片');
      return;
    }

    setState(() => _isProcessing = true);

    try {
      // 显示处理对话框
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(imageFiles.length > 1
                    ? '正在识别 ${imageFiles.length} 张图片内容...'
                    : '正在识别图片内容...'),
                const SizedBox(height: 8),
                Text(
                  '使用模型: ${_settings!.llmModel}',
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
          ),
        );
      }

      // 调用LLM服务处理图片
      final content = imageFiles.length > 1
          ? await _llmService.processImages(imageFiles, _settings!)
          : await _llmService.processImage(imageFiles.first, _settings!);

      // 关闭处理对话框
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 显示结果对话框
      if (mounted) {
        await _showResultDialog(content, imageFiles.first.path, imageFiles.length);
      }

    } catch (e) {
      // 关闭处理对话框
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 显示错误详情
      _showErrorDialog('处理失败', e.toString());
    } finally {
      setState(() => _isProcessing = false);
    }
  }

  Future<void> _showResultDialog(String content, String imagePath, [int imageCount = 1]) async {
    final result = await showDialog<String?>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('识别结果'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: Column(
            children: [
              Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.green),
                  const SizedBox(width: 8),
                  Text(imageCount > 1 ? '$imageCount张图片识别成功！' : '识别成功！'),
                  const Spacer(),
                  Text('${content.length} 字符'),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: SingleChildScrollView(
                    child: Text(
                      content,
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(null),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop('backup'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.cloud_upload, size: 16),
                SizedBox(width: 4),
                Text('备份'),
              ],
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop('save'),
            child: const Text('保存'),
          ),
        ],
      ),
    );

    if (result == 'save') {
      await _saveResult(content, imagePath);
    } else if (result == 'backup') {
      await _backupResult(content, imagePath);
    }
  }

  Future<void> _saveResult(String content, String imagePath) async {
    try {
      // 生成文件名，传递内容以提取标题
      final filename = FileService.generateFilename(null, content: content);

      // 获取保存路径
      final folderPath = _settings!.markdownFolder ?? await FileService.getDefaultImageFolder();

      String? filePath;

      // 实际保存文件
      try {
        filePath = await FileService.saveMarkdownFile(content, filename, folderPath);
      } catch (e) {
        // 文件保存失败，但仍然可以保存到数据库
        // print('文件保存失败: $e'); // 注释掉print语句
      }

      // 保存到数据库
      try {
        final result = ProcessResult(
          title: _extractTitle(content),
          filePath: filePath ?? '$folderPath/$filename.md',
          content: content,
          imagePath: imagePath,
          processedAt: DateTime.now(),
          backupStatus: 'none',
        );

        await DatabaseService.insertResult(result);

        if (filePath != null) {
          _showSuccessSnackBar('结果已保存: $filename.md\n路径: $filePath');
        } else {
          _showSuccessSnackBar('结果已保存到数据库: $filename.md');
        }
      } catch (e) {
        // 数据库保存失败，但文件可能已保存
        if (filePath != null) {
          _showSuccessSnackBar('文件已保存: $filename.md\n路径: $filePath\n(数据库保存失败: $e)');
        } else {
          throw Exception('文件和数据库保存都失败: $e');
        }
      }

    } catch (e) {
      _showErrorSnackBar('保存失败: $e');
    }
  }

  Future<void> _backupResult(String content, String imagePath) async {
    try {
      // 检查云端备份配置
      if (_settings == null || !_settings!.isBackupConfigured) {
        _showErrorSnackBar('请先在设置中配置云端备份');
        return;
      }

      // 生成文件名，使用与保存相同的命名规则
      final filename = FileService.generateFilename(null, content: content);
      final fullFilename = '$filename.md';

      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在备份到云端...'),
            ],
          ),
        ),
      );

      // 创建云端备份服务
      final backupService = CloudBackupService(
        apiUrl: _settings!.backupApiUrl!,
        apiKey: _settings!.backupApiKey!,
      );

      // 执行备份
      final backupResult = await backupService.backupFile(fullFilename, content);

      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (backupResult.success) {
        _showSuccessSnackBar('文件已成功备份到云端\n文件名: $fullFilename');
      } else {
        _showErrorSnackBar('备份失败: ${backupResult.error}');
      }

    } catch (e) {
      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();
      }
      _showErrorSnackBar('备份失败: $e');
    }
  }

  String _extractTitle(String content) {
    // 从内容中提取标题
    final lines = content.split('\n');
    for (final line in lines) {
      final trimmed = line.trim();
      if (trimmed.startsWith('# ')) {
        return trimmed.substring(2).trim();
      }
      if (trimmed.startsWith('## ')) {
        return trimmed.substring(3).trim();
      }
      if (trimmed.isNotEmpty && !trimmed.startsWith('#')) {
        // 如果没有找到标题，使用第一行非空内容
        return trimmed.length > 30 ? '${trimmed.substring(0, 30)}...' : trimmed;
      }
    }
    return '手写识别结果';
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('错误详情：'),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                border: Border.all(color: Colors.red.shade200),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                message,
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 12,
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Text('可能的解决方案：'),
            const Text('• 检查网络连接'),
            const Text('• 验证API密钥是否正确'),
            const Text('• 确认API服务是否可用'),
            const Text('• 检查图片是否清晰'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('了解'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _openSettings();
            },
            child: const Text('检查设置'),
          ),
        ],
      ),
    );
  }

  Future<void> _openSettings() async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(builder: (context) => const SettingsPage()),
    );

    if (result == true) {
      // 设置已更新，重新加载
      await _loadSettings();
    }
  }

  void _viewResults() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const ResultsPage()),
    );
  }

  Future<void> _batchProcess() async {
    if (_settings == null || !_settings!.isLLMConfigured) {
      _showErrorSnackBar('请先配置LLM设置');
      return;
    }

    final watchFolder = _settings!.watchFolder;
    if (watchFolder == null || watchFolder.isEmpty) {
      _showErrorSnackBar('请先在设置中配置监控文件夹');
      return;
    }

    // 检查文件访问权限（批量处理需要更强的权限）
    if (!await _checkFileAccessPermissions()) {
      return;
    }

    try {
      // 使用调试版本获取图片文件
      final result = await FileService.getImageFilesFromDirectoryWithDebug(watchFolder);
      final imageFiles = result['files'] as List<File>;

      if (result['error'] != null) {
        // 显示详细错误信息和解决方案
        String errorMessage = result['error'] as String;
        if (result['debug'] != null) {
          errorMessage += '\n\n调试信息:\n${result['debug']}';
        }

        // 如果是权限问题，显示专门的权限错误对话框
        if (errorMessage.contains('Permission denied') ||
            errorMessage.contains('权限') ||
            errorMessage.contains('访问被拒绝')) {
          _showDirectoryPermissionErrorDialog(watchFolder, errorMessage);
        } else {
          _showErrorSnackBar(errorMessage);
        }
        return;
      }

      if (imageFiles.isEmpty) {
        final allFileNames = result['allFileNames'] as List<String>? ?? [];
        String debugInfo = '监控文件夹中没有找到图片文件\n';
        debugInfo += '支持的格式: jpg, jpeg, png, bmp, gif, webp\n';
        if (allFileNames.isNotEmpty) {
          debugInfo += '找到的文件: ${allFileNames.join(', ')}';
        } else {
          debugInfo += '文件夹为空或无法访问';
        }
        _showErrorSnackBar(debugInfo);
        return;
      }

      // 跳转到批量处理预览页面
      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => BatchProcessPage(
              imageFiles: imageFiles,
              settings: _settings!,
            ),
          ),
        );
      }
    } catch (e) {
      _showErrorSnackBar('读取监控文件夹失败: $e');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
      ),
    );
  }





  @override
  void dispose() {
    _llmService.dispose();
    super.dispose();
  }
}

class InstructionItem extends StatelessWidget {
  final String step;
  final String title;
  final String description;

  const InstructionItem({
    super.key,
    required this.step,
    required this.title,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                step,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
