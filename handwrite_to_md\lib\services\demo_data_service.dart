import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';
import 'file_service.dart';

class DemoDataService {
  static const String _deletedItemsKey = 'deleted_demo_items';
  
  /// 获取已删除的演示项目ID列表
  static Future<Set<int>> getDeletedItemIds() async {
    final prefs = await SharedPreferences.getInstance();
    final deletedIds = prefs.getStringList(_deletedItemsKey) ?? [];
    return deletedIds.map((id) => int.parse(id)).toSet();
  }
  
  /// 标记演示项目为已删除
  static Future<void> markItemAsDeleted(int itemId) async {
    final prefs = await SharedPreferences.getInstance();
    final deletedIds = await getDeletedItemIds();
    deletedIds.add(itemId);
    
    final stringIds = deletedIds.map((id) => id.toString()).toList();
    await prefs.setStringList(_deletedItemsKey, stringIds);
  }
  
  /// 恢复已删除的演示项目（用于测试或重置）
  static Future<void> restoreItem(int itemId) async {
    final prefs = await SharedPreferences.getInstance();
    final deletedIds = await getDeletedItemIds();
    deletedIds.remove(itemId);
    
    final stringIds = deletedIds.map((id) => id.toString()).toList();
    await prefs.setStringList(_deletedItemsKey, stringIds);
  }
  
  /// 清除所有删除记录（重置演示数据）
  static Future<void> resetDemoData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_deletedItemsKey);
  }
  
  /// 尝试删除对应的实际文件
  static Future<bool> tryDeleteDemoFile(String filename) async {
    try {
      // 获取默认文件夹路径
      final defaultFolder = await FileService.getDefaultImageFolder();
      final filePath = '$defaultFolder$filename';
      
      // 尝试删除文件
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      
      // 如果默认路径不存在，尝试其他可能的路径
      final alternativePaths = [
        '/storage/emulated/0/Download/$filename',
        '/storage/emulated/0/Documents/$filename',
        '/storage/emulated/0/HandwriteApp/$filename',
      ];
      
      for (final path in alternativePaths) {
        final altFile = File(path);
        if (await altFile.exists()) {
          await altFile.delete();
          return true;
        }
      }
      
      return false; // 文件不存在，但这不算错误
    } catch (e) {
      // 删除失败，但不影响演示数据的删除
      return false;
    }
  }
  
  /// 获取演示数据统计信息
  static Future<Map<String, dynamic>> getDemoDataStats() async {
    final deletedIds = await getDeletedItemIds();
    return {
      'totalItems': 2, // 当前有2个演示项目
      'deletedItems': deletedIds.length,
      'activeItems': 2 - deletedIds.length,
      'deletedIds': deletedIds.toList(),
    };
  }
}
