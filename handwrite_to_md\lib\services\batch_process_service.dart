import 'dart:io';
import '../models/article_group.dart';
import '../models/app_settings.dart';
import '../models/process_result.dart';
import 'llm_service.dart';
import 'file_service.dart';
import 'database_service.dart';

class BatchProcessService {
  final LLMService _llmService = LLMService();

  // 从图片文件列表创建初始文章组（每张图片一个文章）
  List<ArticleGroup> createInitialArticleGroups(List<File> imageFiles) {
    return imageFiles.asMap().entries.map((entry) {
      final index = entry.key;
      final file = entry.value;
      
      return ArticleGroup(
        id: 'article_$index',
        images: [
          ImageItem(
            id: 'image_${index}_0',
            file: file,
          ),
        ],
        title: '文章 ${index + 1}',
      );
    }).toList();
  }

  // 处理单个文章组
  Future<ProcessResult> processArticleGroup(
    ArticleGroup articleGroup,
    AppSettings settings,
  ) async {
    try {
      String content;
      
      if (articleGroup.isMultiImage) {
        // 多图片处理
        content = await _llmService.processImagesWithNames(
          articleGroup.images.map((item) => item.file).toList(),
          articleGroup.getImageNames(),
          settings,
        );
      } else {
        // 单图片处理
        content = await _llmService.processImage(
          articleGroup.images.first.file,
          settings,
        );
      }

      // 生成文件名，传递内容以提取标题
      final filename = FileService.generateFilename(null, content: content);

      // 获取保存路径
      final folderPath = settings.markdownFolder ??
          await FileService.getDefaultImageFolder();

      // 保存Markdown文件
      final filePath = await FileService.saveMarkdownFile(
        content, 
        filename, 
        folderPath,
      );

      // 创建处理结果
      final result = ProcessResult(
        title: _extractTitle(content),
        filePath: filePath,
        imagePath: articleGroup.images.first.filePath,
        content: content,
        processedAt: DateTime.now(),
      );

      // 保存到数据库
      final id = await DatabaseService.insertResult(result);
      return result.copyWith(id: id);

    } catch (e) {
      throw Exception('处理文章组失败: $e');
    }
  }

  // 批量处理所有文章组
  Future<List<ProcessResult>> processAllArticleGroups(
    List<ArticleGroup> articleGroups,
    AppSettings settings,
    Function(int current, int total, String currentTitle)? onProgress,
  ) async {
    final results = <ProcessResult>[];
    
    for (int i = 0; i < articleGroups.length; i++) {
      final articleGroup = articleGroups[i];
      
      // 更新进度
      onProgress?.call(i + 1, articleGroups.length, articleGroup.title);
      
      try {
        final result = await processArticleGroup(articleGroup, settings);
        results.add(result);
      } catch (e) {
        // 记录错误但继续处理其他文章
        // print('处理文章组 ${articleGroup.title} 失败: $e');
        // 可以选择抛出异常或继续处理
        rethrow;
      }
    }
    
    return results;
  }

  // 从内容中提取标题
  String _extractTitle(String content) {
    final lines = content.split('\n');
    for (final line in lines) {
      final trimmed = line.trim();
      if (trimmed.startsWith('#')) {
        return trimmed.replaceAll('#', '').trim();
      }
      if (trimmed.isNotEmpty && !trimmed.startsWith('```')) {
        return trimmed.length > 50 
            ? '${trimmed.substring(0, 50)}...' 
            : trimmed;
      }
    }
    return '未命名文档';
  }

  void dispose() {
    _llmService.dispose();
  }
}
