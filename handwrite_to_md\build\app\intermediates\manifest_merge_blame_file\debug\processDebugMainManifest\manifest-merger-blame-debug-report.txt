1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.handwrite_to_md"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:12:5-67
15-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:12:22-64
16    <!-- 相机权限 -->
17    <uses-permission android:name="android.permission.CAMERA" /> <!-- 存储权限 (适用于Android 10及以下) -->
17-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:3:5-65
17-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:3:22-62
18    <uses-permission
18-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:5:5-107
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:5:22-77
20        android:maxSdkVersion="32" />
20-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:5:78-104
21    <uses-permission
21-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:6:5-108
22        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
22-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:6:22-78
23        android:maxSdkVersion="28" /> <!-- Android 13+ 照片权限 -->
23-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:6:79-105
24    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" /> <!-- Android 11+ 管理外部存储权限 -->
24-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:8:5-76
24-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:8:22-73
25    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> <!-- 网络状态权限 -->
25-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:10:5-82
25-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:10:22-79
26    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
26-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:14:5-79
26-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:14:22-76
27    <!--
28 Required to query activities that can process text, see:
29         https://developer.android.com/training/package-visibility and
30         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
31
32         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
33    -->
34    <queries>
34-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:53:5-58:15
35        <intent>
35-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:54:9-57:18
36            <action android:name="android.intent.action.PROCESS_TEXT" />
36-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:55:13-72
36-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:55:21-70
37
38            <data android:mimeType="text/plain" />
38-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:56:13-50
38-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:56:19-48
39        </intent>
40        <intent>
40-->[:file_picker] D:\android\handwritetomd\handwrite_to_md\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
41            <action android:name="android.intent.action.GET_CONTENT" />
41-->[:file_picker] D:\android\handwritetomd\handwrite_to_md\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
41-->[:file_picker] D:\android\handwritetomd\handwrite_to_md\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
42
43            <data android:mimeType="*/*" />
43-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:56:13-50
43-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:56:19-48
44        </intent>
45    </queries>
46
47    <permission
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
48        android:name="com.example.handwrite_to_md.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
49        android:protectionLevel="signature" />
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
50
51    <uses-permission android:name="com.example.handwrite_to_md.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
52
53    <application
54        android:name="android.app.Application"
55        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
56        android:debuggable="true"
57        android:extractNativeLibs="true"
58        android:icon="@mipmap/ic_launcher"
59        android:label="ToMDs" >
60        <activity
61            android:name="com.example.handwrite_to_md.MainActivity"
62            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
63            android:exported="true"
64            android:hardwareAccelerated="true"
65            android:launchMode="singleTop"
66            android:taskAffinity=""
67            android:theme="@style/LaunchTheme"
68            android:windowSoftInputMode="adjustResize" >
69
70            <!--
71                 Specifies an Android theme to apply to this Activity as soon as
72                 the Android process has started. This theme is visible to the user
73                 while the Flutter UI initializes. After that, this theme continues
74                 to determine the Window background behind the Flutter UI.
75            -->
76            <meta-data
77                android:name="io.flutter.embedding.android.NormalTheme"
78                android:resource="@style/NormalTheme" />
79
80            <intent-filter>
81                <action android:name="android.intent.action.MAIN" />
82
83                <category android:name="android.intent.category.LAUNCHER" />
84            </intent-filter>
85        </activity>
86        <!--
87             Don't delete the meta-data below.
88             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
89        -->
90        <meta-data
91            android:name="flutterEmbedding"
92            android:value="2" />
93
94        <provider
94-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
95            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
95-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
96            android:authorities="com.example.handwrite_to_md.flutter.image_provider"
96-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
97            android:exported="false"
97-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
98            android:grantUriPermissions="true" >
98-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
99            <meta-data
99-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
100                android:name="android.support.FILE_PROVIDER_PATHS"
100-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
101                android:resource="@xml/flutter_image_picker_file_paths" />
101-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
102        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
103        <service
103-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
104            android:name="com.google.android.gms.metadata.ModuleDependencies"
104-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
105            android:enabled="false"
105-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
106            android:exported="false" >
106-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
107            <intent-filter>
107-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
108                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
108-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
108-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
109            </intent-filter>
110
111            <meta-data
111-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
112                android:name="photopicker_activity:0:required"
112-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
113                android:value="" />
113-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
114        </service>
115
116        <uses-library
116-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
117            android:name="androidx.window.extensions"
117-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
118            android:required="false" />
118-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
119        <uses-library
119-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
120            android:name="androidx.window.sidecar"
120-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
121            android:required="false" />
121-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
122
123        <provider
123-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
124            android:name="androidx.startup.InitializationProvider"
124-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
125            android:authorities="com.example.handwrite_to_md.androidx-startup"
125-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
126            android:exported="false" >
126-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
127            <meta-data
127-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
128                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
128-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
129                android:value="androidx.startup" />
129-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
130            <meta-data
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
131                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
132                android:value="androidx.startup" />
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
133        </provider>
134
135        <receiver
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
136            android:name="androidx.profileinstaller.ProfileInstallReceiver"
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
137            android:directBootAware="false"
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
138            android:enabled="true"
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
139            android:exported="true"
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
140            android:permission="android.permission.DUMP" >
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
141            <intent-filter>
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
142                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
143            </intent-filter>
144            <intent-filter>
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
145                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
146            </intent-filter>
147            <intent-filter>
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
148                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
149            </intent-filter>
150            <intent-filter>
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
151                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
152            </intent-filter>
153        </receiver>
154    </application>
155
156</manifest>
