# handwrite_to_md

# 手写识别转Markdown应用

这是一个基于Flutter开发的手写文字识别应用，可以将手写文字图片转换为Markdown格式文档。

## 功能特性

- 📷 **拍照识别**: 直接拍照识别手写内容
- 🖼️ **相册选择**: 从相册选择图片进行识别
- ⚙️ **自定义配置**: 支持多种LLM模型配置
- ☁️ **云端备份**: 自动备份识别结果到云端
- 📝 **Markdown预览**: 实时预览转换结果
- 💾 **本地存储**: 自动保存识别历史

## 技术架构

### 核心技术栈
- **Flutter**: 跨平台移动应用开发框架
- **Dart**: 编程语言
- **SQLite**: 本地数据库存储
- **HTTP/Dio**: 网络请求处理
- **LLM API**: 大语言模型图像识别

### 主要依赖包
```yaml
dependencies:
  flutter:
    sdk: flutter
  shared_preferences: ^2.2.2      # 设置管理
  dio: ^5.4.0                     # HTTP请求
  form_field_validator: ^1.1.0    # 表单验证
  flutter_secure_storage: ^9.0.0  # 加密存储
  sqflite: ^2.3.0                # 数据库
  path: ^1.8.3                   # 路径操作
  file_picker: ^8.1.2            # 文件选择
  image_picker: ^1.0.4           # 图片选择
  permission_handler: ^11.1.0     # 权限管理
  intl: ^0.19.0                  # 日期格式化
  provider: ^6.1.1               # 状态管理
```

## 项目结构

```
lib/
├── main.dart                    # 应用入口
├── models/                      # 数据模型
│   ├── app_settings.dart       # 应用设置模型
│   ├── backup_result.dart      # 备份结果模型
│   ├── markdown_file.dart      # Markdown文件模型
│   └── process_result.dart     # 处理结果模型
├── services/                    # 服务层
│   ├── database_service.dart   # 数据库服务
│   ├── settings_repository.dart # 设置仓库
│   ├── cloud_backup_service.dart # 云端备份服务
│   ├── llm_service.dart        # LLM服务
│   └── file_service.dart       # 文件服务
└── pages/                       # 页面
    ├── home_page.dart          # 主页
    ├── settings_page.dart      # 设置页面
    ├── results_page.dart       # 结果页面
    ├── backup_result_page.dart # 备份结果页面
    └── markdown_preview_page.dart # Markdown预览页面
```

## 使用说明

### 1. 配置LLM服务

在使用图片识别功能之前，需要先配置LLM服务：

1. 点击"配置LLM设置"按钮
2. 填写以下信息：
   - **Base URL**: LLM服务的API地址（如：`https://api.openai.com/v1`）
   - **API Key**: 您的API密钥
   - **模型**: 选择支持图像识别的模型（如：`gpt-4-vision-preview`）
   - **系统提示词**: 自定义识别指令

### 2. 支持的LLM模型

- **OpenAI GPT-4 Vision**: `gpt-4-vision-preview`, `gpt-4o`
- **Anthropic Claude 3**: `claude-3-sonnet-20240229`, `claude-3-opus-20240229`, `claude-3-haiku-20240307`

### 3. 图片识别流程

1. 选择图片来源（拍照/相册/文件）
2. 应用自动调用LLM API进行识别
3. 识别结果自动转换为Markdown格式
4. 保存到本地文件和数据库
5. 可选择备份到云端

### 4. 云端备份配置

如需使用云端备份功能：

1. 在设置页面配置备份API地址和密钥
2. 测试连接确保配置正确
3. 在结果页面可以单独或批量备份文件

## 开发进度

### ✅ 已完成功能
- [x] 基础项目架构搭建
- [x] 数据模型设计
- [x] 数据库服务实现
- [x] 设置管理功能
- [x] LLM服务集成
- [x] 文件管理服务
- [x] 云端备份服务
- [x] 用户界面设计
- [x] 简化版本演示

### 🚧 开发中功能
- [ ] 图片选择和处理
- [ ] LLM API调用集成
- [ ] 结果预览和编辑
- [ ] 批量处理功能
- [ ] 错误处理和重试机制

### 📋 计划功能
- [ ] 离线OCR支持
- [ ] 多语言支持
- [ ] 主题切换
- [ ] 导出功能增强
- [ ] 分享功能
- [ ] 历史记录搜索

## 安装和运行

### 环境要求
- Flutter SDK 3.7.2+
- Dart SDK 3.0+
- Android Studio / VS Code
- Android SDK (Android开发)
- Xcode (iOS开发)

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd handwrite_to_md
```

2. 安装依赖
```bash
flutter pub get
```

3. 运行应用
```bash
flutter run
```

## 配置说明

### Android权限配置

应用需要以下权限：
- 相机权限：拍照功能
- 存储权限：保存文件
- 网络权限：API调用

### iOS权限配置

在`ios/Runner/Info.plist`中添加：
```xml
<key>NSCameraUsageDescription</key>
<string>需要相机权限来拍照识别手写内容</string>
<key>NSPhotoLibraryUsageDescription</key>
<string>需要相册权限来选择图片进行识别</string>
```

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

MIT License
