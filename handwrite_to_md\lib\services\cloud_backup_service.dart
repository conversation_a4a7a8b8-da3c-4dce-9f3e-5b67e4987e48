import 'package:dio/dio.dart';
import '../models/backup_result.dart';
import '../models/markdown_file.dart';

class CloudBackupService {
  final String apiUrl;
  final String apiKey;
  final Dio _dio;

  CloudBackupService({required this.apiUrl, required this.apiKey})
      : _dio = Dio();

  Future<BackupResult> backupFile(String filename, String content) async {
    try {
      final response = await _dio.post(
        apiUrl,
        data: {
          'apikey': apiKey,
          'action': 'create',
          'filename': 'Handwrite/$filename',
          'content': content,
        },
        options: Options(
          headers: {'Content-Type': 'application/json'},
          sendTimeout: const Duration(seconds: 30),
          receiveTimeout: const Duration(seconds: 30),
        ),
      );

      if (response.statusCode == 200) {
        return BackupResult(
          success: true,
          filename: filename,
          timestamp: DateTime.now(),
        );
      } else {
        return BackupResult(
          success: false,
          filename: filename,
          error: '备份失败: ${response.statusCode}',
          timestamp: DateTime.now(),
        );
      }
    } catch (e) {
      return BackupResult(
        success: false,
        filename: filename,
        error: '备份异常: $e',
        timestamp: DateTime.now(),
      );
    }
  }

  Future<List<BackupResult>> backupMultipleFiles(
    List<MarkdownFile> files,
    Function(int, int) onProgress,
  ) async {
    List<BackupResult> results = [];

    for (int i = 0; i < files.length; i++) {
      final result = await backupFile(files[i].filename, files[i].content);
      results.add(result);
      onProgress(i + 1, files.length);

      // 避免频繁请求，添加延迟
      if (i < files.length - 1) {
        await Future.delayed(const Duration(milliseconds: 500));
      }
    }

    return results;
  }

  Future<bool> testConnection() async {
    try {
      final response = await _dio.post(
        apiUrl,
        data: {
          'apikey': apiKey,
          'action': 'test',
        },
        options: Options(
          sendTimeout: const Duration(seconds: 10),
          receiveTimeout: const Duration(seconds: 10),
        ),
      );
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  Future<BackupResult> backupSingleMarkdownFile(MarkdownFile file) async {
    return await backupFile(file.filename, file.content);
  }

  // 获取备份状态
  Future<Map<String, dynamic>?> getBackupStatus(String filename) async {
    try {
      final response = await _dio.post(
        apiUrl,
        data: {
          'apikey': apiKey,
          'action': 'status',
          'filename': 'Handwrite/$filename',
        },
        options: Options(
          sendTimeout: const Duration(seconds: 10),
          receiveTimeout: const Duration(seconds: 10),
        ),
      );

      if (response.statusCode == 200) {
        return response.data;
      }
    } catch (e) {
      // 忽略错误，返回null
    }
    return null;
  }

  // 删除备份文件
  Future<bool> deleteBackupFile(String filename) async {
    try {
      final response = await _dio.post(
        apiUrl,
        data: {
          'apikey': apiKey,
          'action': 'delete',
          'filename': 'Handwrite/$filename',
        },
        options: Options(
          sendTimeout: const Duration(seconds: 10),
          receiveTimeout: const Duration(seconds: 10),
        ),
      );

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  // 列出所有备份文件
  Future<List<String>?> listBackupFiles() async {
    try {
      final response = await _dio.post(
        apiUrl,
        data: {
          'apikey': apiKey,
          'action': 'list',
          'path': 'Handwrite/',
        },
        options: Options(
          sendTimeout: const Duration(seconds: 10),
          receiveTimeout: const Duration(seconds: 10),
        ),
      );

      if (response.statusCode == 200 && response.data is List) {
        return List<String>.from(response.data);
      }
    } catch (e) {
      // 忽略错误
    }
    return null;
  }

  void dispose() {
    _dio.close();
  }
}
