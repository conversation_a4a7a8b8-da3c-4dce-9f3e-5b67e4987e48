import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/process_result.dart';

class DatabaseService {
  static Database? _database;
  static const String _databaseName = 'handwrite_to_md.db';
  static const int _databaseVersion = 1;

  // 表名
  static const String _tableResults = 'results';
  static const String _tableSettings = 'settings';
  static const String _tableBackupRecords = 'backup_records';

  static Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  static Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), _databaseName);
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  static Future<void> _onCreate(Database db, int version) async {
    // 创建结果表
    await db.execute('''
      CREATE TABLE $_tableResults (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        file_path TEXT NOT NULL,
        image_path TEXT NOT NULL,
        content TEXT NOT NULL,
        processed_at TEXT NOT NULL,
        backup_status TEXT DEFAULT 'none',
        backup_time TEXT
      )
    ''');

    // 创建设置表
    await db.execute('''
      CREATE TABLE $_tableSettings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        value TEXT,
        is_encrypted BOOLEAN DEFAULT FALSE,
        updated_at TEXT NOT NULL
      )
    ''');

    // 创建备份记录表
    await db.execute('''
      CREATE TABLE $_tableBackupRecords (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_path TEXT NOT NULL,
        filename TEXT NOT NULL,
        backup_status TEXT NOT NULL,
        backup_time TEXT NOT NULL,
        error_message TEXT
      )
    ''');
  }

  static Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // 处理数据库升级
    if (oldVersion < newVersion) {
      // 添加备份相关字段（如果从旧版本升级）
      try {
        await db.execute('ALTER TABLE $_tableResults ADD COLUMN backup_status TEXT DEFAULT "none"');
        await db.execute('ALTER TABLE $_tableResults ADD COLUMN backup_time TEXT');
      } catch (e) {
        // 字段可能已存在，忽略错误
      }
    }
  }

  // 结果相关操作
  static Future<int> insertResult(ProcessResult result) async {
    final db = await database;
    return await db.insert(_tableResults, result.toMap());
  }

  static Future<List<ProcessResult>> getAllResults() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableResults,
      orderBy: 'processed_at DESC',
    );
    return List.generate(maps.length, (i) => ProcessResult.fromMap(maps[i]));
  }

  static Future<ProcessResult?> getResult(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableResults,
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return ProcessResult.fromMap(maps.first);
    }
    return null;
  }

  static Future<int> updateResult(ProcessResult result) async {
    final db = await database;
    return await db.update(
      _tableResults,
      result.toMap(),
      where: 'id = ?',
      whereArgs: [result.id],
    );
  }

  static Future<int> deleteResult(int id) async {
    final db = await database;
    return await db.delete(
      _tableResults,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // 批量删除所有结果
  static Future<int> deleteAllResults() async {
    final db = await database;
    return await db.delete(_tableResults);
  }

  // 设置相关操作
  static Future<void> saveSetting(String key, String value, {bool isEncrypted = false}) async {
    final db = await database;
    await db.insert(
      _tableSettings,
      {
        'key': key,
        'value': value,
        'is_encrypted': isEncrypted ? 1 : 0,
        'updated_at': DateTime.now().toIso8601String(),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  static Future<String?> getSetting(String key) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableSettings,
      where: 'key = ?',
      whereArgs: [key],
    );
    if (maps.isNotEmpty) {
      return maps.first['value'];
    }
    return null;
  }

  static Future<int> deleteSetting(String key) async {
    final db = await database;
    return await db.delete(
      _tableSettings,
      where: 'key = ?',
      whereArgs: [key],
    );
  }

  // 备份记录相关操作
  static Future<int> insertBackupRecord({
    required String filePath,
    required String filename,
    required String backupStatus,
    required DateTime backupTime,
    String? errorMessage,
  }) async {
    final db = await database;
    return await db.insert(_tableBackupRecords, {
      'file_path': filePath,
      'filename': filename,
      'backup_status': backupStatus,
      'backup_time': backupTime.toIso8601String(),
      'error_message': errorMessage,
    });
  }

  static Future<List<Map<String, dynamic>>> getBackupRecords() async {
    final db = await database;
    return await db.query(
      _tableBackupRecords,
      orderBy: 'backup_time DESC',
    );
  }

  // 关闭数据库
  static Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}
