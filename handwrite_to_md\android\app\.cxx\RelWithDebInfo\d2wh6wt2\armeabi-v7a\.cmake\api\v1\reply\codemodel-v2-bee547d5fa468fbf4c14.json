{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "D:/android/handwritetomd/handwrite_to_md/android/app/.cxx/RelWithDebInfo/d2wh6wt2/armeabi-v7a", "source": "D:/Tools/flutter/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}