import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/process_result.dart';
import '../models/backup_result.dart';
import '../models/app_settings.dart';
import '../services/database_service.dart';
import '../services/settings_repository.dart';
import '../services/cloud_backup_service.dart';
import '../services/file_service.dart';
import 'backup_result_page.dart';
import 'markdown_preview_page.dart';

class ResultsPage extends StatefulWidget {
  final ProcessResult? initialResult;

  const ResultsPage({super.key, this.initialResult});

  @override
  State<ResultsPage> createState() => _ResultsPageState();
}

class _ResultsPageState extends State<ResultsPage> {
  List<ProcessResult> _results = [];
  bool _isLoading = false;
  ProcessResult? _selectedResult;

  @override
  void initState() {
    super.initState();
    _selectedResult = widget.initialResult;
    _loadResults();
  }

  Future<void> _loadResults() async {
    setState(() => _isLoading = true);
    try {
      final results = await DatabaseService.getAllResults();
      setState(() => _results = results);
    } catch (e) {
      _showErrorSnackBar('加载结果失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('处理结果'),
        actions: [
          if (_results.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.delete_sweep),
              onPressed: _deleteAllResults,
              tooltip: '删除所有',
            ),
          IconButton(
            icon: const Icon(Icons.cloud_upload),
            onPressed: _results.isNotEmpty ? _showBackupDialog : null,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadResults,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _results.isEmpty
              ? const Center(
                  child: Text(
                    '暂无处理结果\n返回主页开始识别图片',
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.grey),
                  ),
                )
              : ListView.builder(
                  itemCount: _results.length,
                  itemBuilder: (context, index) {
                    final result = _results[index];
                    final isSelected = _selectedResult?.id == result.id;
                    
                    return Card(
                      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                      color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : null,
                      child: ListTile(
                        leading: const Icon(Icons.description),
                        title: isSelected
                            ? SizedBox(
                                height: 20,
                                child: SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: Text(result.title),
                                ),
                              )
                            : Text(
                                result.title,
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            isSelected
                                ? SizedBox(
                                    height: 16,
                                    child: SingleChildScrollView(
                                      scrollDirection: Axis.horizontal,
                                      child: Text(
                                        '${DateFormat('yyyy-MM-dd HH:mm').format(result.processedAt)} • ${result.filename}',
                                        style: const TextStyle(fontSize: 14),
                                      ),
                                    ),
                                  )
                                : Text(
                                    '${DateFormat('yyyy-MM-dd HH:mm').format(result.processedAt)} • ${result.filename}',
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  ),
                            if (result.backupStatus != null && result.backupStatus != 'none')
                              Row(
                                children: [
                                  Icon(
                                    result.backupStatus == 'success' 
                                        ? Icons.cloud_done 
                                        : Icons.cloud_off,
                                    size: 16,
                                    color: result.backupStatus == 'success' 
                                        ? Colors.green 
                                        : Colors.red,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    result.backupStatus == 'success' ? '已备份' : '备份失败',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: result.backupStatus == 'success' 
                                          ? Colors.green 
                                          : Colors.red,
                                    ),
                                  ),
                                ],
                              ),
                          ],
                        ),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.preview),
                              onPressed: () => _previewMarkdown(result),
                            ),
                            IconButton(
                              icon: const Icon(Icons.cloud_upload),
                              onPressed: () => _backupSingleFile(result),
                            ),
                            PopupMenuButton<String>(
                              onSelected: (value) => _handleMenuAction(value, result),
                              itemBuilder: (context) => [
                                const PopupMenuItem(
                                  value: 'edit',
                                  child: Row(
                                    children: [
                                      Icon(Icons.edit),
                                      SizedBox(width: 8),
                                      Text('编辑'),
                                    ],
                                  ),
                                ),
                                const PopupMenuItem(
                                  value: 'share',
                                  child: Row(
                                    children: [
                                      Icon(Icons.share),
                                      SizedBox(width: 8),
                                      Text('分享'),
                                    ],
                                  ),
                                ),
                                const PopupMenuItem(
                                  value: 'delete',
                                  child: Row(
                                    children: [
                                      Icon(Icons.delete, color: Colors.red),
                                      SizedBox(width: 8),
                                      Text('删除', style: TextStyle(color: Colors.red)),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        onTap: () => _selectResult(result),
                      ),
                    );
                  },
                ),
    );
  }

  void _selectResult(ProcessResult result) {
    setState(() {
      _selectedResult = _selectedResult?.id == result.id ? null : result;
    });
  }

  void _previewMarkdown(ProcessResult result) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => MarkdownPreviewPage(
          title: result.title,
          content: result.content,
          filePath: result.filePath,
        ),
      ),
    );
  }

  Future<void> _backupSingleFile(ProcessResult result) async {
    try {
      final settingsRepository = await SettingsRepository.create();
      final settings = await settingsRepository.loadSettings();
      
      if (!settings.isBackupConfigured) {
        _showErrorSnackBar('请先配置云端备份设置');
        return;
      }

      final backupService = CloudBackupService(
        apiUrl: settings.backupApiUrl!,
        apiKey: settings.backupApiKey!,
      );

      _showLoadingDialog('正在备份文件...');

      final backupResult = await backupService.backupFile(
        result.filename,
        result.content,
      );

      if (mounted) {
        Navigator.of(context).pop(); // 关闭加载对话框
      }

      if (backupResult.success) {
        // 更新数据库中的备份状态
        final updatedResult = result.copyWith(
          backupStatus: 'success',
          backupTime: DateTime.now(),
        );
        await DatabaseService.updateResult(updatedResult);
        
        _showSuccessSnackBar('文件备份成功');
        await _loadResults(); // 刷新列表
      } else {
        _showErrorSnackBar('备份失败: ${backupResult.error}');
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // 关闭加载对话框
      }
      _showErrorSnackBar('备份失败: $e');
    }
  }

  void _showBackupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('备份到云端'),
        content: const Text('确定要将所有文件备份到云端吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _backupAllFiles();
            },
            child: const Text('备份'),
          ),
        ],
      ),
    );
  }

  Future<void> _backupAllFiles() async {
    try {
      final settingsRepository = await SettingsRepository.create();
      final settings = await settingsRepository.loadSettings();
      
      if (!settings.isBackupConfigured) {
        _showErrorSnackBar('请先配置云端备份设置');
        return;
      }

      final backupService = CloudBackupService(
        apiUrl: settings.backupApiUrl!,
        apiKey: settings.backupApiKey!,
      );

      // 显示进度对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在备份文件...'),
            ],
          ),
        ),
      );

      List<BackupResult> results = [];
      for (int i = 0; i < _results.length; i++) {
        final result = _results[i];
        final backupResult = await backupService.backupFile(
          result.filename,
          result.content,
        );
        results.add(backupResult);

        // 更新数据库状态
        if (backupResult.success) {
          final updatedResult = result.copyWith(
            backupStatus: 'success',
            backupTime: DateTime.now(),
          );
          await DatabaseService.updateResult(updatedResult);
        }

        // 添加延迟避免频繁请求
        if (i < _results.length - 1) {
          await Future.delayed(const Duration(milliseconds: 500));
        }
      }

      if (mounted) {
        Navigator.of(context).pop(); // 关闭进度对话框
        
        // 显示备份结果
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => BackupResultPage(results: results),
          ),
        );
        
        await _loadResults(); // 刷新列表
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // 关闭进度对话框
      }
      _showErrorSnackBar('批量备份失败: $e');
    }
  }

  void _handleMenuAction(String action, ProcessResult result) {
    switch (action) {
      case 'edit':
        _editResult(result);
        break;
      case 'share':
        _shareResult(result);
        break;
      case 'delete':
        _deleteResult(result);
        break;
    }
  }

  void _editResult(ProcessResult result) {
    // TODO: 实现编辑功能
    _showInfoSnackBar('编辑功能开发中');
  }

  void _shareResult(ProcessResult result) {
    // TODO: 实现分享功能
    _showInfoSnackBar('分享功能开发中');
  }

  Future<void> _deleteResult(ProcessResult result) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除确认'),
        content: Text('确定要删除"${result.title}"吗？此操作不可撤销。\n\n此操作将删除：\n• 数据库中的记录\n• 对应的Markdown文件'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // 显示删除进度
        _showLoadingDialog('正在删除...');

        // 删除数据库记录
        await DatabaseService.deleteResult(result.id!);

        // 删除对应的Markdown文件
        final markdownDeleted = await FileService.deleteFiles([result.filePath]);

        if (mounted) {
          Navigator.of(context).pop(); // 关闭进度对话框
        }

        await _loadResults();

        // 如果删除的是当前选中的结果，清除选中状态
        if (_selectedResult?.id == result.id) {
          setState(() => _selectedResult = null);
        }

        // 显示删除结果
        final markdownSuccess = markdownDeleted[result.filePath] ?? false;
        if (markdownSuccess) {
          _showSuccessSnackBar('删除成功（包括Markdown文件）');
        } else {
          _showSuccessSnackBar('删除成功（Markdown文件未找到或删除失败）');
        }
      } catch (e) {
        if (mounted) {
          Navigator.of(context).pop(); // 关闭进度对话框
        }
        _showErrorSnackBar('删除失败: $e');
      }
    }
  }

  Future<void> _deleteAllResults() async {
    if (_results.isEmpty) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除所有确认'),
        content: Text('确定要删除所有 ${_results.length} 个处理结果吗？\n\n此操作将删除：\n• 数据库中的所有记录\n• 对应的Markdown文件\n\n此操作不可撤销！'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('删除所有'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // 显示删除进度
        _showLoadingDialog('正在删除所有结果...');

        // 收集所有Markdown文件路径
        List<String> markdownFiles = [];
        final resultCount = _results.length;

        for (final result in _results) {
          markdownFiles.add(result.filePath);
        }

        // 删除数据库记录
        await DatabaseService.deleteAllResults();

        // 删除Markdown文件
        final markdownResults = await FileService.deleteFiles(markdownFiles);

        if (mounted) {
          Navigator.of(context).pop(); // 关闭进度对话框
        }

        // 统计删除结果
        int deletedMarkdown = markdownResults.values.where((success) => success).length;

        // 清除选中状态并重新加载
        setState(() => _selectedResult = null);
        await _loadResults();

        _showSuccessSnackBar(
          '删除完成\n数据库记录: $resultCount 个\nMarkdown文件: $deletedMarkdown/${markdownFiles.length} 个'
        );
      } catch (e) {
        if (mounted) {
          Navigator.of(context).pop(); // 关闭进度对话框
        }
        _showErrorSnackBar('批量删除失败: $e');
      }
    }
  }

  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(message),
          ],
        ),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}
