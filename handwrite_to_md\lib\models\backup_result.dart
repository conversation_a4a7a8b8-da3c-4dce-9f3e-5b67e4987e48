class BackupResult {
  final bool success;
  final String filename;
  final String? error;
  final DateTime timestamp;

  BackupResult({
    required this.success,
    required this.filename,
    this.error,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'filename': filename,
      'error': error,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory BackupResult.fromJson(Map<String, dynamic> json) {
    return BackupResult(
      success: json['success'],
      filename: json['filename'],
      error: json['error'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}
