import 'package:flutter_test/flutter_test.dart';
import 'package:handwrite_to_md/services/file_service.dart';

void main() {
  group('FileService generateFilename tests', () {
    test('should generate filename with title from markdown content', () {
      // 测试用例1：有标题的markdown内容
      const content1 = '''
# 这是一个测试标题
这是正文内容
''';
      final filename1 = FileService.generateFilename(null, content: content1);
      expect(filename1, matches(r'^这是一个测试标题_\d{8}_\d{4}$'));
    });

    test('should generate filename with cleaned title (remove punctuation)', () {
      // 测试用例2：标题包含标点符号
      const content2 = '''
# 这是一个！@#\$%^&*()测试标题？？？
这是正文内容
''';
      final filename2 = FileService.generateFilename(null, content: content2);
      expect(filename2, matches(r'^这是一个测试标题_\d{8}_\d{4}$'));
    });

    test('should limit title to 10 characters', () {
      // 测试用例3：标题超过10个字符
      const content3 = '''
# 这是一个非常非常非常长的测试标题内容
这是正文内容
''';
      final filename3 = FileService.generateFilename(null, content: content3);
      expect(filename3, matches(r'^这是一个非常非常非常_\d{8}_\d{4}$'));
    });

    test('should use second level heading if no first level heading', () {
      // 测试用例4：使用二级标题
      const content4 = '''
## 二级标题测试
这是正文内容
''';
      final filename4 = FileService.generateFilename(null, content: content4);
      expect(filename4, matches(r'^二级标题测试_\d{8}_\d{4}$'));
    });

    test('should use first line if no heading found', () {
      // 测试用例5：没有标题，使用第一行内容
      const content5 = '''
这是第一行内容作为标题
这是第二行内容
''';
      final filename5 = FileService.generateFilename(null, content: content5);
      expect(filename5, matches(r'^这是第一行内容作为标_\d{8}_\d{4}$'));
    });

    test('should fallback to handwrite when no title found', () {
      // 测试用例6：没有可用的标题内容
      const content6 = '''
```
代码块
```
''';
      final filename6 = FileService.generateFilename(null, content: content6);
      expect(filename6, matches(r'^handwrite_\d{8}_\d{4}$'));
    });

    test('should fallback to handwrite when content is empty', () {
      // 测试用例7：空内容
      const content7 = '';
      final filename7 = FileService.generateFilename(null, content: content7);
      expect(filename7, matches(r'^handwrite_\d{8}_\d{4}$'));
    });

    test('should use custom name when provided', () {
      // 测试用例8：提供自定义名称
      const content8 = '''
# 这是标题
内容
''';
      final filename8 = FileService.generateFilename('自定义名称', content: content8);
      expect(filename8, matches(r'^自定义名称_\d{8}_\d{4}$'));
    });

    test('should handle mixed Chinese and English', () {
      // 测试用例9：中英文混合标题
      const content9 = '''
# Hello世界Test测试
内容
''';
      final filename9 = FileService.generateFilename(null, content: content9);
      expect(filename9, matches(r'^Hello世界Tes_\d{8}_\d{4}$'));
    });

    test('should handle English only title', () {
      // 测试用例10：纯英文标题
      const content10 = '''
# Hello World Test
Content here
''';
      final filename10 = FileService.generateFilename(null, content: content10);
      expect(filename10, matches(r'^HelloWorld_\d{8}_\d{4}$'));
    });
  });
}
