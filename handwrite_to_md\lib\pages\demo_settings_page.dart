import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:file_picker/file_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/app_settings.dart';
import '../services/llm_service.dart';
import '../services/cloud_backup_service.dart';

class DemoSettingsPage extends StatefulWidget {
  const DemoSettingsPage({super.key});

  @override
  State<DemoSettingsPage> createState() => _DemoSettingsPageState();
}

class _DemoSettingsPageState extends State<DemoSettingsPage> {
  final _formKey = GlobalKey<FormState>();
  
  // 控制器
  final _markdownFolderController = TextEditingController();
  final _watchFolderController = TextEditingController();
  final _llmBaseUrlController = TextEditingController();
  final _llmApiKeyController = TextEditingController();
  final _llmModelController = TextEditingController();
  final _systemPromptController = TextEditingController();
  final _backupApiUrlController = TextEditingController();
  final _backupApiKeyController = TextEditingController();

  // 状态变量
  bool _obscureApiKey = true;
  bool _obscureBackupKey = true;
  bool? _backupTestResult;
  bool? _llmTestResult;

  @override
  void initState() {
    super.initState();
    _loadDemoData();
  }

  Future<void> _loadDemoData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 从SharedPreferences加载设置，如果没有则使用默认值
      _markdownFolderController.text = prefs.getString('markdown_folder') ?? '/storage/emulated/0/HandwriteApp/markdown/';
      _watchFolderController.text = prefs.getString('watch_folder') ?? '/storage/emulated/0/HandwriteApp/watch/';
      _llmBaseUrlController.text = prefs.getString('llm_base_url') ?? 'https://api.openai.com/v1';
      _llmApiKeyController.text = prefs.getString('llm_api_key') ?? '';
      _llmModelController.text = prefs.getString('llm_model') ?? 'gpt-4o';
      _systemPromptController.text = prefs.getString('system_prompt') ?? '''你是一个专业的手写文字识别助手。请分析图片中的手写内容：

1. 准确识别所有手写文字内容
2. 保持原文的段落结构和逻辑顺序
3. 输出格式必须是标准Markdown格式
4. 如果有标题，请用适当的#标记层级
5. 如果有列表，请用-或数字标记
6. 忽略涂改、装饰性内容和无关标记
7. 如果内容不清晰，请用[不清晰]标注

请直接输出转换结果，不要添加其他说明文字。''';
      _backupApiUrlController.text = prefs.getString('backup_api_url') ?? '';
      _backupApiKeyController.text = prefs.getString('backup_api_key') ?? '';

      setState(() {}); // 刷新UI
    } catch (e) {
      _showErrorSnackBar('加载设置失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveSettings,
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildFolderSection(),
            const Divider(height: 32),
            _buildLLMSection(),
            const Divider(height: 32),
            _buildBackupSection(),
            const SizedBox(height: 32),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildFolderSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('文件夹设置', style: Theme.of(context).textTheme.headlineSmall),
        const SizedBox(height: 16),

        // Markdown保存文件夹
        Text('Markdown保存文件夹', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _markdownFolderController,
                decoration: const InputDecoration(
                  labelText: 'Markdown文件保存路径',
                  hintText: '/storage/emulated/0/HandwriteApp/markdown/',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入Markdown保存文件夹路径';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: _selectMarkdownFolder,
              child: const Text('选择'),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // 监控文件夹
        Text('监控文件夹', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _watchFolderController,
                decoration: const InputDecoration(
                  labelText: '图片监控文件夹路径',
                  hintText: '/storage/emulated/0/HandwriteApp/watch/',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入监控文件夹路径';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: _selectWatchFolder,
              child: const Text('选择'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLLMSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text('LLM配置', style: Theme.of(context).textTheme.headlineSmall),
            const Spacer(),
            if (_llmTestResult != null)
              Icon(
                _llmTestResult! ? Icons.check_circle : Icons.error,
                color: _llmTestResult! ? Colors.green : Colors.red,
              ),
          ],
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _llmBaseUrlController,
          decoration: const InputDecoration(
            labelText: 'Base URL',
            hintText: 'https://api.openai.com/v1',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '请输入LLM Base URL';
            }
            final uri = Uri.tryParse(value);
            if (uri == null || !uri.hasAbsolutePath) {
              return '请输入有效的URL';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _llmApiKeyController,
          decoration: InputDecoration(
            labelText: 'API Key',
            hintText: '输入您的API Key',
            border: const OutlineInputBorder(),
            suffixIcon: IconButton(
              icon: Icon(_obscureApiKey ? Icons.visibility : Icons.visibility_off),
              onPressed: () => setState(() => _obscureApiKey = !_obscureApiKey),
            ),
          ),
          obscureText: _obscureApiKey,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '请输入API Key';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _llmModelController,
          decoration: const InputDecoration(
            labelText: '模型',
            hintText: 'gpt-4o',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '请输入模型名称';
            }
            return null;
          },

        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _systemPromptController,
          decoration: const InputDecoration(
            labelText: '系统提示词',
            hintText: '输入自定义系统提示词',
            border: OutlineInputBorder(),
          ),
          maxLines: 5,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '请输入系统提示词';
            }
            return null;
          },
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            ElevatedButton(
              onPressed: _resetToDefaultPrompt,
              child: const Text('恢复默认提示词'),
            ),
            const SizedBox(width: 16),
            ElevatedButton(
              onPressed: _testLLMConnection,
              child: const Text('测试连接'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBackupSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text('云端备份', style: Theme.of(context).textTheme.headlineSmall),
            const Spacer(),
            if (_backupTestResult != null)
              Icon(
                _backupTestResult! ? Icons.check_circle : Icons.error,
                color: _backupTestResult! ? Colors.green : Colors.red,
              ),
          ],
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _backupApiUrlController,
          decoration: const InputDecoration(
            labelText: '备份API地址',
            hintText: 'https://your-backup-api.com/api',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              final uri = Uri.tryParse(value);
              if (uri == null || !uri.hasAbsolutePath) {
                return '请输入有效的URL';
              }
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _backupApiKeyController,
          decoration: InputDecoration(
            labelText: '备份API Key',
            hintText: '输入备份服务的API Key',
            border: const OutlineInputBorder(),
            suffixIcon: IconButton(
              icon: Icon(_obscureBackupKey ? Icons.visibility : Icons.visibility_off),
              onPressed: () => setState(() => _obscureBackupKey = !_obscureBackupKey),
            ),
          ),
          obscureText: _obscureBackupKey,
        ),
        const SizedBox(height: 16),
        ElevatedButton(
          onPressed: _testBackupConnection,
          child: const Text('测试连接'),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: _saveSettings,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text('保存设置'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: OutlinedButton(
            onPressed: _resetSettings,
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text('重置设置'),
          ),
        ),
      ],
    );
  }

  Future<void> _selectMarkdownFolder() async {
    try {
      // 请求存储权限
      if (await _requestStoragePermission()) {
        String? selectedDirectory = await FilePicker.platform.getDirectoryPath();
        if (selectedDirectory != null) {
          setState(() {
            _markdownFolderController.text = selectedDirectory;
          });
          _showSuccessSnackBar('Markdown文件夹选择成功');
        }
      } else {
        _showErrorSnackBar('需要存储权限才能选择文件夹');
      }
    } catch (e) {
      _showErrorSnackBar('选择Markdown文件夹失败: $e');
    }
  }

  Future<void> _selectWatchFolder() async {
    try {
      // 请求存储权限
      if (await _requestStoragePermission()) {
        String? selectedDirectory = await FilePicker.platform.getDirectoryPath();
        if (selectedDirectory != null) {
          setState(() {
            _watchFolderController.text = selectedDirectory;
          });
          _showSuccessSnackBar('监控文件夹选择成功');
        }
      } else {
        _showErrorSnackBar('需要存储权限才能选择文件夹');
      }
    } catch (e) {
      _showErrorSnackBar('选择监控文件夹失败: $e');
    }
  }

  Future<bool> _requestStoragePermission() async {
    try {
      // 检查并请求存储权限
      var status = await Permission.storage.status;
      if (status.isDenied) {
        status = await Permission.storage.request();
      }

      // 对于Android 13+，可能需要请求照片权限
      if (status.isDenied) {
        var photoStatus = await Permission.photos.status;
        if (photoStatus.isDenied) {
          photoStatus = await Permission.photos.request();
        }
        return photoStatus.isGranted;
      }

      return status.isGranted;
    } catch (e) {
      // 权限请求失败，但在某些设备上可能不需要权限
      return true; // 在某些情况下可能不需要权限
    }
  }

  void _resetToDefaultPrompt() {
    const defaultPrompt = """你是一个专业的手写文字识别助手。请分析图片中的手写内容：

1. 准确识别所有手写文字内容
2. 保持原文的段落结构和逻辑顺序
3. 输出格式必须是标准Markdown格式
4. 如果有标题，请用适当的#标记层级
5. 如果有列表，请用-或数字标记
6. 忽略涂改、装饰性内容和无关标记
7. 如果内容不清晰，请用[不清晰]标注
8. 如果有表格，请用|和-来表示
9. 如果内容中有用五角星开头的内容，则将该内容作为markdown文件的标题，如果没有，你来根据内容自动生成一个标题

请直接输出转换结果，不要添加其他说明文字。""";

    setState(() {
      _systemPromptController.text = defaultPrompt;
    });
    _showSuccessSnackBar('已恢复默认提示词');
  }

  Future<void> _testLLMConnection() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _llmTestResult = null);

    try {
      final settings = AppSettings(
        llmBaseUrl: _llmBaseUrlController.text,
        llmApiKey: _llmApiKeyController.text,
        llmModel: _llmModelController.text,
      );

      final llmService = LLMService();
      final result = await llmService.testConnection(settings);

      setState(() => _llmTestResult = result);

      if (result) {
        _showSuccessSnackBar('LLM连接测试成功');
      } else {
        _showErrorSnackBar('LLM连接测试失败，请检查配置');
      }
    } catch (e) {
      setState(() => _llmTestResult = false);
      _showErrorSnackBar('LLM连接测试失败: $e');
    }
  }

  Future<void> _testBackupConnection() async {
    final apiUrl = _backupApiUrlController.text;
    final apiKey = _backupApiKeyController.text;

    if (apiUrl.isEmpty || apiKey.isEmpty) {
      _showErrorSnackBar('请先填写备份API地址和密钥');
      return;
    }

    setState(() => _backupTestResult = null);

    try {
      final backupService = CloudBackupService(
        apiUrl: apiUrl,
        apiKey: apiKey,
      );

      final result = await backupService.testConnection();

      setState(() => _backupTestResult = result);

      if (result) {
        _showSuccessSnackBar('备份连接测试成功');
      } else {
        _showErrorSnackBar('备份连接测试失败，请检查API地址和密钥');
      }
    } catch (e) {
      setState(() => _backupTestResult = false);
      _showErrorSnackBar('备份连接测试失败: $e');
    }
  }

  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      final prefs = await SharedPreferences.getInstance();

      // 保存设置到SharedPreferences
      await prefs.setString('markdown_folder', _markdownFolderController.text);
      await prefs.setString('watch_folder', _watchFolderController.text);
      await prefs.setString('llm_base_url', _llmBaseUrlController.text);
      await prefs.setString('llm_api_key', _llmApiKeyController.text);
      await prefs.setString('llm_model', _llmModelController.text);
      await prefs.setString('system_prompt', _systemPromptController.text);
      await prefs.setString('backup_api_url', _backupApiUrlController.text);
      await prefs.setString('backup_api_key', _backupApiKeyController.text);

      _showSuccessSnackBar('设置保存成功');
      if (mounted) {
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      _showErrorSnackBar('保存设置失败: $e');
    }
  }

  void _resetSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重置设置'),
        content: const Text('确定要重置所有设置吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _loadDemoData();
              _showSuccessSnackBar('设置已重置');
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }



  @override
  void dispose() {
    _markdownFolderController.dispose();
    _watchFolderController.dispose();
    _llmBaseUrlController.dispose();
    _llmApiKeyController.dispose();
    _llmModelController.dispose();
    _systemPromptController.dispose();
    _backupApiUrlController.dispose();
    _backupApiKeyController.dispose();
    super.dispose();
  }
}
