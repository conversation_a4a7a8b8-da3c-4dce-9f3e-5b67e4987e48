import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import '../models/app_settings.dart';

class LLMService {
  final Dio _dio;
  
  LLMService() : _dio = Dio();

  Future<String> processImage(File imageFile, AppSettings settings) async {
    return await processImages([imageFile], settings);
  }

  Future<String> processImages(List<File> imageFiles, AppSettings settings) async {
    // 生成默认名称
    final names = imageFiles.asMap().entries.map((entry) {
      final index = entry.key;
      final filename = entry.value.path.split('/').last;
      return imageFiles.length > 1 ? '图片${index + 1} ($filename)' : filename;
    }).toList();

    return processImagesWithNames(imageFiles, names, settings);
  }

  Future<String> processImagesWithNames(
    List<File> imageFiles,
    List<String> imageNames,
    AppSettings settings,
  ) async {
    if (!settings.isLLMConfigured) {
      throw Exception('LLM配置不完整');
    }

    if (imageFiles.isEmpty) {
      throw Exception('没有提供图片文件');
    }

    if (imageFiles.length != imageNames.length) {
      throw Exception('图片文件数量与名称数量不匹配');
    }

    try {
      // 读取所有图片文件并转换为base64
      List<Map<String, String>> imageData = [];
      for (int i = 0; i < imageFiles.length; i++) {
        final file = imageFiles[i];
        final bytes = await file.readAsBytes();
        final base64Image = base64Encode(bytes);

        imageData.add({
          'base64': base64Image,
          'name': imageNames[i],
        });
      }

      // 根据不同的模型构建请求
      if (settings.llmModel!.startsWith('gpt-')) {
        return await _processWithOpenAI(imageData, settings);
      } else if (settings.llmModel!.startsWith('claude-')) {
        return await _processWithClaude(imageData, settings);
      } else {
        return await _processWithOpenAI(imageData, settings);
      }
    } catch (e) {
      throw Exception('处理图片失败: $e');
    }
  }

  Future<String> _processWithOpenAI(List<Map<String, String>> imageData, AppSettings settings) async {
    // 构建content数组
    List<Map<String, dynamic>> content = [
      {
        'type': 'text',
        'text': imageData.length > 1
            ? '请识别这些图片中的手写文字内容，并转换为Markdown格式。请按图片顺序整理内容。'
            : '请识别这张图片中的手写文字内容，并转换为Markdown格式。',
      },
    ];

    // 添加所有图片
    for (final image in imageData) {
      content.add({
        'type': 'image_url',
        'name': image['name'],
        'image_url': {
          'url': 'data:image/jpeg;base64,${image['base64']}',
        },
      });
    }

    final response = await _dio.post(
      '${settings.llmBaseUrl}/chat/completions',
      options: Options(
        headers: {
          'Authorization': 'Bearer ${settings.llmApiKey}',
          'Content-Type': 'application/json',
        },
        sendTimeout: const Duration(seconds: 60),
        receiveTimeout: const Duration(seconds: 60),
      ),
      data: {
        'model': settings.llmModel,
        'messages': [
          {
            'role': 'system',
            'content': settings.systemPrompt,
          },
          {
            'role': 'user',
            'content': content,
          },
        ],
        'max_tokens': 4000,
        'temperature': 0.1,
      },
    );

    if (response.statusCode == 200) {
      final data = response.data;
      if (data['choices'] != null && data['choices'].isNotEmpty) {
        return data['choices'][0]['message']['content'];
      } else {
        throw Exception('API返回数据格式错误');
      }
    } else {
      throw Exception('API请求失败: ${response.statusCode}');
    }
  }

  Future<String> _processWithClaude(List<Map<String, String>> imageData, AppSettings settings) async {
    // 构建content数组
    List<Map<String, dynamic>> content = [
      {
        'type': 'text',
        'text': imageData.length > 1
            ? '请识别这些图片中的手写文字内容，并转换为Markdown格式。请按图片顺序整理内容。'
            : '请识别这张图片中的手写文字内容，并转换为Markdown格式。',
      },
    ];

    // 添加所有图片
    for (final image in imageData) {
      content.add({
        'type': 'image',
        'name': image['name'],
        'source': {
          'type': 'base64',
          'media_type': 'image/jpeg',
          'data': image['base64'],
        },
      });
    }

    final response = await _dio.post(
      '${settings.llmBaseUrl}/messages',
      options: Options(
        headers: {
          'x-api-key': settings.llmApiKey,
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01',
        },
        sendTimeout: const Duration(seconds: 60),
        receiveTimeout: const Duration(seconds: 60),
      ),
      data: {
        'model': settings.llmModel,
        'max_tokens': 4000,
        'system': settings.systemPrompt,
        'messages': [
          {
            'role': 'user',
            'content': content,
          },
        ],
      },
    );

    if (response.statusCode == 200) {
      final data = response.data;
      if (data['content'] != null && data['content'].isNotEmpty) {
        return data['content'][0]['text'];
      } else {
        throw Exception('API返回数据格式错误');
      }
    } else {
      throw Exception('API请求失败: ${response.statusCode}');
    }
  }

  Future<bool> testConnection(AppSettings settings) async {
    if (!settings.isLLMConfigured) {
      return false;
    }

    try {
      if (settings.llmModel!.startsWith('gpt-')) {
        return await _testOpenAIConnection(settings);
      } else if (settings.llmModel!.startsWith('claude-')) {
        return await _testClaudeConnection(settings);
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testOpenAIConnection(AppSettings settings) async {
    try {
      final response = await _dio.get(
        '${settings.llmBaseUrl}/models',
        options: Options(
          headers: {
            'Authorization': 'Bearer ${settings.llmApiKey}',
          },
          sendTimeout: const Duration(seconds: 10),
          receiveTimeout: const Duration(seconds: 10),
        ),
      );
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testClaudeConnection(AppSettings settings) async {
    try {
      final response = await _dio.post(
        '${settings.llmBaseUrl}/messages',
        options: Options(
          headers: {
            'x-api-key': settings.llmApiKey,
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01',
          },
          sendTimeout: const Duration(seconds: 10),
          receiveTimeout: const Duration(seconds: 10),
        ),
        data: {
          'model': settings.llmModel,
          'max_tokens': 10,
          'messages': [
            {
              'role': 'user',
              'content': 'test',
            },
          ],
        },
      );
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  void dispose() {
    _dio.close();
  }
}
