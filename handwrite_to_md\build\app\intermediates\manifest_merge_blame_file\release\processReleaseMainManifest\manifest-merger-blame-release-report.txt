1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.handwrite_to_md"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!-- 相机权限 -->
11    <uses-permission android:name="android.permission.CAMERA" />
11-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:4:5-65
11-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:4:22-62
12    <!-- 存储权限 (兼容所有Android版本) -->
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:6:5-80
13-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:6:22-77
14    <uses-permission
14-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:7:5-9:40
15        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
15-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:7:22-78
16        android:maxSdkVersion="28" />
16-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:8:9-35
17    <!-- Android 13+ 照片权限 -->
18    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
18-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:11:5-76
18-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:11:22-73
19    <!-- Android 11+ 管理外部存储权限 -->
20    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
20-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:13:5-82
20-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:13:22-79
21    <!-- 网络权限 -->
22    <uses-permission android:name="android.permission.INTERNET" />
22-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:15:5-67
22-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:15:22-64
23    <!-- 网络状态权限 -->
24    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
24-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:17:5-79
24-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:17:22-76
25    <!-- 请求忽略电池优化，避免后台权限问题 -->
26    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
26-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:19:5-95
26-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:19:22-92
27    <!--
28         Required to query activities that can process text, see:
29         https://developer.android.com/training/package-visibility and
30         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
31
32         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
33    -->
34    <queries>
34-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:58:5-63:15
35        <intent>
35-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:59:9-62:18
36            <action android:name="android.intent.action.PROCESS_TEXT" />
36-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:60:13-72
36-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:60:21-70
37
38            <data android:mimeType="text/plain" />
38-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:61:13-50
38-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:61:19-48
39        </intent>
40        <intent>
40-->[:file_picker] D:\android\handwritetomd\handwrite_to_md\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:18
41            <action android:name="android.intent.action.GET_CONTENT" />
41-->[:file_picker] D:\android\handwritetomd\handwrite_to_md\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-72
41-->[:file_picker] D:\android\handwritetomd\handwrite_to_md\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:21-69
42
43            <data android:mimeType="*/*" />
43-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:61:13-50
43-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:61:19-48
44        </intent>
45    </queries>
46
47    <permission
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
48        android:name="com.example.handwrite_to_md.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
49        android:protectionLevel="signature" />
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
50
51    <uses-permission android:name="com.example.handwrite_to_md.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
52
53    <application
54        android:name="android.app.Application"
54-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:23:9-42
55        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
56        android:extractNativeLibs="true"
57        android:icon="@mipmap/ic_launcher"
57-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:24:9-43
58        android:label="ToMDs" >
58-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:22:9-30
59        <activity
59-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:25:9-46:20
60            android:name="com.example.handwrite_to_md.MainActivity"
60-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:26:13-41
61            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
61-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:31:13-163
62            android:exported="true"
62-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:27:13-36
63            android:hardwareAccelerated="true"
63-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:32:13-47
64            android:launchMode="singleTop"
64-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:28:13-43
65            android:taskAffinity=""
65-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:29:13-36
66            android:theme="@style/LaunchTheme"
66-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:30:13-47
67            android:windowSoftInputMode="adjustResize" >
67-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:33:13-55
68
69            <!--
70                 Specifies an Android theme to apply to this Activity as soon as
71                 the Android process has started. This theme is visible to the user
72                 while the Flutter UI initializes. After that, this theme continues
73                 to determine the Window background behind the Flutter UI.
74            -->
75            <meta-data
75-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:38:13-41:17
76                android:name="io.flutter.embedding.android.NormalTheme"
76-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:39:15-70
77                android:resource="@style/NormalTheme" />
77-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:40:15-52
78
79            <intent-filter>
79-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:42:13-45:29
80                <action android:name="android.intent.action.MAIN" />
80-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:43:17-68
80-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:43:25-66
81
82                <category android:name="android.intent.category.LAUNCHER" />
82-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:44:17-76
82-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:44:27-74
83            </intent-filter>
84        </activity>
85        <!--
86             Don't delete the meta-data below.
87             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
88        -->
89        <meta-data
89-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:49:9-51:33
90            android:name="flutterEmbedding"
90-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:50:13-44
91            android:value="2" />
91-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:51:13-30
92
93        <provider
93-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-17:20
94            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
94-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-82
95            android:authorities="com.example.handwrite_to_md.flutter.image_provider"
95-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
96            android:exported="false"
96-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
97            android:grantUriPermissions="true" >
97-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-47
98            <meta-data
98-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:75
99                android:name="android.support.FILE_PROVIDER_PATHS"
99-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-67
100                android:resource="@xml/flutter_image_picker_file_paths" />
100-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:17-72
101        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
102        <service
102-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:19
103            android:name="com.google.android.gms.metadata.ModuleDependencies"
103-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-78
104            android:enabled="false"
104-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-36
105            android:exported="false" >
105-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
106            <intent-filter>
106-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-26:29
107                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
107-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-94
107-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-91
108            </intent-filter>
109
110            <meta-data
110-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-30:36
111                android:name="photopicker_activity:0:required"
111-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-63
112                android:value="" />
112-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-33
113        </service>
114
115        <uses-library
115-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
116            android:name="androidx.window.extensions"
116-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
117            android:required="false" />
117-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
118        <uses-library
118-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
119            android:name="androidx.window.sidecar"
119-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
120            android:required="false" />
120-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
121
122        <provider
122-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
123            android:name="androidx.startup.InitializationProvider"
123-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
124            android:authorities="com.example.handwrite_to_md.androidx-startup"
124-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
125            android:exported="false" >
125-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
126            <meta-data
126-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
127                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
127-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
128                android:value="androidx.startup" />
128-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
129            <meta-data
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
130                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
131                android:value="androidx.startup" />
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
132        </provider>
133
134        <receiver
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
135            android:name="androidx.profileinstaller.ProfileInstallReceiver"
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
136            android:directBootAware="false"
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
137            android:enabled="true"
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
138            android:exported="true"
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
139            android:permission="android.permission.DUMP" >
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
140            <intent-filter>
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
141                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
142            </intent-filter>
143            <intent-filter>
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
144                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
145            </intent-filter>
146            <intent-filter>
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
147                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
148            </intent-filter>
149            <intent-filter>
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
150                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
151            </intent-filter>
152        </receiver>
153    </application>
154
155</manifest>
