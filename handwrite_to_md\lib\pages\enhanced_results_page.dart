import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/app_settings.dart';
import '../services/cloud_backup_service.dart';
import '../services/demo_data_service.dart';
import 'demo_results_page.dart';

class EnhancedResultsPage extends StatefulWidget {
  const EnhancedResultsPage({super.key});

  @override
  State<EnhancedResultsPage> createState() => _EnhancedResultsPageState();
}

class _EnhancedResultsPageState extends State<EnhancedResultsPage> {
  List<DemoResult> _results = [];
  AppSettings? _settings;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadResults();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final settings = AppSettings(
        markdownFolder: prefs.getString('markdown_folder'),
        watchFolder: prefs.getString('watch_folder'),
        llmBaseUrl: prefs.getString('llm_base_url'),
        llmApiKey: prefs.getString('llm_api_key'),
        llmModel: prefs.getString('llm_model'),
        systemPrompt: prefs.getString('system_prompt'),
        backupApiUrl: prefs.getString('backup_api_url'),
        backupApiKey: prefs.getString('backup_api_key'),
      );
      
      setState(() => _settings = settings);
    } catch (e) {
      _showErrorSnackBar('加载设置失败: $e');
    }
  }

  Future<void> _loadResults() async {
    setState(() => _isLoading = true);

    // 获取已删除的项目ID列表
    final deletedIds = await DemoDataService.getDeletedItemIds();

    // 加载演示数据和真实数据
    final allResults = [
      DemoResult(
        id: 1,
        title: '学习笔记 - Flutter开发',
        filename: 'flutter_notes_${DateFormat('yyyyMMdd_HHmm').format(DateTime.now())}.md',
        content: '''# Flutter开发学习笔记

## 今日学习内容

### 1. 状态管理
- **setState()**: 最基础的状态管理
- **Provider**: 推荐的状态管理方案
- **Riverpod**: 更现代的状态管理
- **Bloc**: 适合大型项目

### 2. 网络请求
```dart
// 使用Dio进行HTTP请求
final dio = Dio();
final response = await dio.get('https://api.example.com/data');
```

### 3. 数据持久化
- **SharedPreferences**: 简单键值对存储
- **SQLite**: 关系型数据库
- **Hive**: 轻量级NoSQL数据库

### 4. 权限管理
```dart
// 请求权限
var status = await Permission.camera.request();
if (status.isGranted) {
  // 权限已授予
}
```

## 重要概念
- **Widget树**: Flutter的核心概念
- **BuildContext**: Widget在树中的位置
- **Key**: Widget的唯一标识
- **生命周期**: initState, build, dispose

## 最佳实践
1. 使用const构造函数优化性能
2. 避免在build方法中创建对象
3. 合理使用ListView.builder处理长列表
4. 及时释放资源避免内存泄漏

## 下一步学习计划
- [ ] 深入学习动画系统
- [ ] 掌握自定义Widget开发
- [ ] 学习平台特定功能集成
- [ ] 性能优化技巧''',
        processedAt: DateTime.now().subtract(const Duration(days: 1)),
        backupStatus: 'none',
      ),
      DemoResult(
        id: 2,
        title: '购物清单',
        filename: 'shopping_list_${DateFormat('yyyyMMdd_HHmm').format(DateTime.now().subtract(const Duration(days: 2)))}.md',
        content: '''# 购物清单

## 生活用品
- [x] 洗发水
- [x] 牙膏
- [ ] 毛巾
- [ ] 洗衣液

## 食品
- [x] 大米
- [x] 面条
- [ ] 鸡蛋
- [ ] 牛奶
- [ ] 苹果
- [ ] 香蕉

## 办公用品
- [ ] 笔记本
- [ ] 圆珠笔
- [ ] 便利贴

## 预算
总预算：500元
已花费：120元
剩余：380元''',
        processedAt: DateTime.now().subtract(const Duration(days: 2)),
        backupStatus: 'failed',
      ),
    ];

    // 过滤掉已删除的项目
    _results = allResults.where((result) => !deletedIds.contains(result.id)).toList();

    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('处理结果'),
        actions: [
          IconButton(
            icon: const Icon(Icons.cloud_upload),
            onPressed: _settings?.isBackupConfigured == true ? _showBackupDialog : _showBackupNotConfigured,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadResults,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _results.isEmpty
              ? const Center(
                  child: Text(
                    '暂无处理结果\n返回主页开始识别图片',
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.grey),
                  ),
                )
              : Column(
                  children: [
                    if (_settings?.isBackupConfigured != true)
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        margin: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade50,
                          border: Border.all(color: Colors.orange.shade200),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.warning, color: Colors.orange.shade700),
                            const SizedBox(width: 8),
                            const Expanded(
                              child: Text(
                                '云端备份未配置，请在设置中配置备份服务',
                                style: TextStyle(fontSize: 12),
                              ),
                            ),
                          ],
                        ),
                      ),
                    Expanded(
                      child: ListView.builder(
                        itemCount: _results.length,
                        itemBuilder: (context, index) {
                          final result = _results[index];
                          
                          return Card(
                            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                            child: ListTile(
                              leading: const Icon(Icons.description),
                              title: Text(result.title),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '${DateFormat('yyyy-MM-dd HH:mm').format(result.processedAt)} • ${result.filename}',
                                  ),
                                  const SizedBox(height: 4),
                                  Row(
                                    children: [
                                      Icon(
                                        result.backupStatus == 'success' 
                                            ? Icons.cloud_done 
                                            : result.backupStatus == 'failed'
                                                ? Icons.cloud_off
                                                : Icons.cloud_queue,
                                        size: 16,
                                        color: result.backupStatus == 'success' 
                                            ? Colors.green 
                                            : result.backupStatus == 'failed'
                                                ? Colors.red
                                                : Colors.grey,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        result.backupStatus == 'success' 
                                            ? '已备份' 
                                            : result.backupStatus == 'failed'
                                                ? '备份失败'
                                                : '未备份',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: result.backupStatus == 'success' 
                                              ? Colors.green 
                                              : result.backupStatus == 'failed'
                                                  ? Colors.red
                                                  : Colors.grey,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  IconButton(
                                    icon: const Icon(Icons.preview),
                                    onPressed: () => _previewResult(result),
                                  ),
                                  if (_settings?.isBackupConfigured == true)
                                    IconButton(
                                      icon: const Icon(Icons.cloud_upload),
                                      onPressed: () => _backupSingleFile(result),
                                    ),
                                  PopupMenuButton<String>(
                                    onSelected: (value) => _handleMenuAction(value, result),
                                    itemBuilder: (context) => [
                                      const PopupMenuItem(
                                        value: 'share',
                                        child: Row(
                                          children: [
                                            Icon(Icons.share),
                                            SizedBox(width: 8),
                                            Text('分享'),
                                          ],
                                        ),
                                      ),
                                      const PopupMenuItem(
                                        value: 'delete',
                                        child: Row(
                                          children: [
                                            Icon(Icons.delete, color: Colors.red),
                                            SizedBox(width: 8),
                                            Text('删除', style: TextStyle(color: Colors.red)),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              onTap: () => _previewResult(result),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
    );
  }

  void _previewResult(DemoResult result) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DemoPreviewPage(result: result),
      ),
    );
  }

  void _showBackupNotConfigured() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('备份未配置'),
        content: const Text('请先在设置中配置云端备份服务的API地址和密钥。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('了解'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // 可以导航到设置页面
            },
            child: const Text('去设置'),
          ),
        ],
      ),
    );
  }

  Future<void> _backupSingleFile(DemoResult result) async {
    if (_settings == null || !_settings!.isBackupConfigured) {
      _showBackupNotConfigured();
      return;
    }

    try {
      _showLoadingDialog('正在备份文件...');

      final backupService = CloudBackupService(
        apiUrl: _settings!.backupApiUrl!,
        apiKey: _settings!.backupApiKey!,
      );

      final backupResult = await backupService.backupFile(
        result.filename,
        result.content,
      );

      if (mounted) {
        Navigator.of(context).pop(); // 关闭加载对话框
      }

      if (backupResult.success) {
        setState(() {
          result.backupStatus = 'success';
        });
        _showSuccessSnackBar('文件备份成功');
      } else {
        setState(() {
          result.backupStatus = 'failed';
        });
        _showErrorSnackBar('备份失败: ${backupResult.error}');
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // 关闭加载对话框
      }
      setState(() {
        result.backupStatus = 'failed';
      });
      _showErrorSnackBar('备份失败: $e');
    }
  }

  void _showBackupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('备份到云端'),
        content: const Text('确定要将所有文件备份到云端吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _backupAllFiles();
            },
            child: const Text('备份'),
          ),
        ],
      ),
    );
  }

  Future<void> _backupAllFiles() async {
    if (_settings == null || !_settings!.isBackupConfigured) {
      _showBackupNotConfigured();
      return;
    }

    try {
      final backupService = CloudBackupService(
        apiUrl: _settings!.backupApiUrl!,
        apiKey: _settings!.backupApiKey!,
      );

      // 显示进度对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text('正在备份 ${_results.length} 个文件...'),
            ],
          ),
        ),
      );

      int successCount = 0;
      int failedCount = 0;

      for (int i = 0; i < _results.length; i++) {
        final result = _results[i];
        try {
          final backupResult = await backupService.backupFile(
            result.filename,
            result.content,
          );

          if (backupResult.success) {
            result.backupStatus = 'success';
            successCount++;
          } else {
            result.backupStatus = 'failed';
            failedCount++;
          }
        } catch (e) {
          result.backupStatus = 'failed';
          failedCount++;
        }

        // 添加延迟避免频繁请求
        if (i < _results.length - 1) {
          await Future.delayed(const Duration(milliseconds: 500));
        }
      }

      if (mounted) {
        Navigator.of(context).pop(); // 关闭进度对话框
        setState(() {}); // 刷新UI

        // 显示结果
        _showSuccessSnackBar('备份完成：成功 $successCount 个，失败 $failedCount 个');
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // 关闭进度对话框
      }
      _showErrorSnackBar('批量备份失败: $e');
    }
  }

  void _handleMenuAction(String action, DemoResult result) {
    switch (action) {
      case 'share':
        _showInfoSnackBar('分享功能开发中');
        break;
      case 'delete':
        _deleteResult(result);
        break;
    }
  }

  Future<void> _deleteResult(DemoResult result) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除确认'),
        content: Text('确定要删除"${result.title}"吗？此操作不可撤销。\n\n注意：如果存在对应的实际文件，也会被删除。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // 显示删除进度
        _showLoadingDialog('正在删除...');

        // 标记为已删除（持久化）
        await DemoDataService.markItemAsDeleted(result.id);

        // 尝试删除对应的实际文件
        final fileDeleted = await DemoDataService.tryDeleteDemoFile(result.filename);

        if (mounted) {
          Navigator.of(context).pop(); // 关闭进度对话框
        }

        // 从当前列表中移除
        setState(() {
          _results.remove(result);
        });

        // 显示删除结果
        if (fileDeleted) {
          _showSuccessSnackBar('删除成功（包括对应文件）');
        } else {
          _showSuccessSnackBar('删除成功（未找到对应文件）');
        }
      } catch (e) {
        if (mounted) {
          Navigator.of(context).pop(); // 关闭进度对话框
        }
        _showErrorSnackBar('删除失败: $e');
      }
    }
  }

  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(message),
          ],
        ),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}
