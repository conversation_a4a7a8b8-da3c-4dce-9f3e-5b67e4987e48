技术路线
1. 新增技术栈组件
设置管理：shared_preferences
云端备份：dio (HTTP请求)
表单验证：form_field_validator
加密存储：flutter_secure_storage (存储敏感信息如API Key)
2. 扩展的核心模块
A. 设置管理模块 (SettingsManager)
dartclass SettingsManager {
  // 图片文件夹设置
  String? defaultImageFolder;
  
  // LLM配置
  String? llmBaseUrl;
  String? llmApiKey;
  String? llmModel;
  String? systemPrompt;
  
  // 云端备份配置
  String? backupApiUrl;
  String? backupApiKey;
  
  // 保存设置
  Future<void> saveSettings();
  
  // 加载设置
  Future<void> loadSettings();
  
  // 验证设置
  bool validateLLMSettings();
  bool validateBackupSettings();
}
B. 云端备份模块 (CloudBackupService)
dartclass CloudBackupService {
  final String apiUrl;
  final String apiKey;
  
  CloudBackupService({required this.apiUrl, required this.apiKey});
  
  // 备份单个文件
  Future<BackupResult> backupFile(String filename, String content);
  
  // 批量备份
  Future<List<BackupResult>> backupMultipleFiles(List<MarkdownFile> files);
  
  // 检查API连接
  Future<bool> testConnection();
}
3. 更新的数据库设计
sql-- 设置表
CREATE TABLE settings (
    id INTEGER PRIMARY KEY,
    key TEXT UNIQUE NOT NULL,
    value TEXT,
    is_encrypted BOOLEAN DEFAULT FALSE,
    updated_at DATETIME
);

-- 备份记录表
CREATE TABLE backup_records (
    id INTEGER PRIMARY KEY,
    file_path TEXT NOT NULL,
    filename TEXT NOT NULL,
    backup_status TEXT, -- 'success', 'failed', 'pending'
    backup_time DATETIME,
    error_message TEXT
);

-- 在原有results表中新增备份状态字段
ALTER TABLE results ADD COLUMN backup_status TEXT DEFAULT 'none';
ALTER TABLE results ADD COLUMN backup_time DATETIME;
4. 设置界面设计
A. 设置页面结构
dartclass SettingsPage extends StatefulWidget {
  @override
  _SettingsPageState createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  final _formKey = GlobalKey<FormState>();
  
  // 控制器
  final _imagesFolderController = TextEditingController();
  final _llmBaseUrlController = TextEditingController();
  final _llmApiKeyController = TextEditingController();
  final _llmModelController = TextEditingController();
  final _systemPromptController = TextEditingController();
  final _backupApiUrlController = TextEditingController();
  final _backupApiKeyController = TextEditingController();
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('设置')),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: EdgeInsets.all(16),
          children: [
            _buildImageFolderSection(),
            Divider(),
            _buildLLMSection(),
            Divider(),
            _buildBackupSection(),
            SizedBox(height: 32),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }
}
B. 设置页面各部分实现
dart// 图片文件夹设置
Widget _buildImageFolderSection() {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text('图片文件夹', style: Theme.of(context).textTheme.headlineSmall),
      SizedBox(height: 8),
      Row(
        children: [
          Expanded(
            child: TextFormField(
              controller: _imagesFolderController,
              decoration: InputDecoration(
                labelText: '默认图片文件夹路径',
                hintText: '/storage/emulated/0/HandwriteApp/',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入图片文件夹路径';
                }
                return null;
              },
            ),
          ),
          SizedBox(width: 8),
          ElevatedButton(
            onPressed: _selectImageFolder,
            child: Text('选择'),
          ),
        ],
      ),
    ],
  );
}

// LLM配置设置
Widget _buildLLMSection() {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text('LLM配置', style: Theme.of(context).textTheme.headlineSmall),
      SizedBox(height: 16),
      TextFormField(
        controller: _llmBaseUrlController,
        decoration: InputDecoration(
          labelText: 'Base URL',
          hintText: 'https://api.openai.com/v1',
          border: OutlineInputBorder(),
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return '请输入LLM Base URL';
          }
          if (!Uri.tryParse(value)?.hasAbsolutePath == true) {
            return '请输入有效的URL';
          }
          return null;
        },
      ),
      SizedBox(height: 16),
      TextFormField(
        controller: _llmApiKeyController,
        decoration: InputDecoration(
          labelText: 'API Key',
          hintText: '输入您的API Key',
          border: OutlineInputBorder(),
          suffixIcon: IconButton(
            icon: Icon(_obscureApiKey ? Icons.visibility : Icons.visibility_off),
            onPressed: () => setState(() => _obscureApiKey = !_obscureApiKey),
          ),
        ),
        obscureText: _obscureApiKey,
        validator: (value) {
          if (value == null || value.isEmpty) {
            return '请输入API Key';
          }
          return null;
        },
      ),
      SizedBox(height: 16),
      DropdownButtonFormField<String>(
        value: _selectedModel,
        decoration: InputDecoration(
          labelText: '模型',
          border: OutlineInputBorder(),
        ),
        items: [
          DropdownMenuItem(value: 'gpt-4-vision-preview', child: Text('GPT-4 Vision')),
          DropdownMenuItem(value: 'claude-3-sonnet-20240229', child: Text('Claude 3 Sonnet')),
          DropdownMenuItem(value: 'claude-3-opus-20240229', child: Text('Claude 3 Opus')),
        ],
        onChanged: (value) => setState(() => _selectedModel = value),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return '请选择模型';
          }
          return null;
        },
      ),
      SizedBox(height: 16),
      TextFormField(
        controller: _systemPromptController,
        decoration: InputDecoration(
          labelText: '系统提示词',
          hintText: '输入自定义系统提示词',
          border: OutlineInputBorder(),
        ),
        maxLines: 5,
        validator: (value) {
          if (value == null || value.isEmpty) {
            return '请输入系统提示词';
          }
          return null;
        },
      ),
      SizedBox(height: 8),
      ElevatedButton(
        onPressed: _resetToDefaultPrompt,
        child: Text('恢复默认提示词'),
      ),
    ],
  );
}

// 云端备份设置
Widget _buildBackupSection() {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text('云端备份', style: Theme.of(context).textTheme.headlineSmall),
      SizedBox(height: 16),
      TextFormField(
        controller: _backupApiUrlController,
        decoration: InputDecoration(
          labelText: '备份API地址',
          hintText: 'https://your-backup-api.com/api',
          border: OutlineInputBorder(),
        ),
        validator: (value) {
          if (value != null && value.isNotEmpty) {
            if (!Uri.tryParse(value)?.hasAbsolutePath == true) {
              return '请输入有效的URL';
            }
          }
          return null;
        },
      ),
      SizedBox(height: 16),
      TextFormField(
        controller: _backupApiKeyController,
        decoration: InputDecoration(
          labelText: '备份API Key',
          hintText: '输入备份服务的API Key',
          border: OutlineInputBorder(),
          suffixIcon: IconButton(
            icon: Icon(_obscureBackupKey ? Icons.visibility : Icons.visibility_off),
            onPressed: () => setState(() => _obscureBackupKey = !_obscureBackupKey),
          ),
        ),
        obscureText: _obscureBackupKey,
      ),
      SizedBox(height: 16),
      Row(
        children: [
          ElevatedButton(
            onPressed: _testBackupConnection,
            child: Text('测试连接'),
          ),
          SizedBox(width: 16),
          if (_backupTestResult != null)
            Icon(
              _backupTestResult! ? Icons.check_circle : Icons.error,
              color: _backupTestResult! ? Colors.green : Colors.red,
            ),
        ],
      ),
    ],
  );
}
5. 云端备份功能实现
A. 备份服务类
dartclass CloudBackupService {
  final String apiUrl;
  final String apiKey;
  final Dio _dio;
  
  CloudBackupService({required this.apiUrl, required this.apiKey}) 
    : _dio = Dio();
  
  Future<BackupResult> backupFile(String filename, String content) async {
    try {
      final response = await _dio.post(
        apiUrl,
        data: {
          'apikey': apiKey,
          'action': 'create',
          'filename': 'Handwrite/$filename',
          'content': content,
        },
        options: Options(
          headers: {'Content-Type': 'application/json'},
          sendTimeout: Duration(seconds: 30),
          receiveTimeout: Duration(seconds: 30),
        ),
      );
      
      if (response.statusCode == 200) {
        return BackupResult(
          success: true,
          filename: filename,
          timestamp: DateTime.now(),
        );
      } else {
        return BackupResult(
          success: false,
          filename: filename,
          error: '备份失败: ${response.statusCode}',
          timestamp: DateTime.now(),
        );
      }
    } catch (e) {
      return BackupResult(
        success: false,
        filename: filename,
        error: '备份异常: $e',
        timestamp: DateTime.now(),
      );
    }
  }
  
  Future<List<BackupResult>> backupMultipleFiles(
    List<MarkdownFile> files,
    Function(int, int) onProgress,
  ) async {
    List<BackupResult> results = [];
    
    for (int i = 0; i < files.length; i++) {
      final result = await backupFile(files[i].filename, files[i].content);
      results.add(result);
      onProgress(i + 1, files.length);
      
      // 避免频繁请求，添加延迟
      if (i < files.length - 1) {
        await Future.delayed(Duration(milliseconds: 500));
      }
    }
    
    return results;
  }
  
  Future<bool> testConnection() async {
    try {
      final response = await _dio.post(
        apiUrl,
        data: {
          'apikey': apiKey,
          'action': 'test',
        },
        options: Options(
          sendTimeout: Duration(seconds: 10),
          receiveTimeout: Duration(seconds: 10),
        ),
      );
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}
B. 备份结果页面
dartclass BackupResultPage extends StatelessWidget {
  final List<BackupResult> results;
  
  const BackupResultPage({Key? key, required this.results}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final successCount = results.where((r) => r.success).length;
    final failedCount = results.length - successCount;
    
    return Scaffold(
      appBar: AppBar(title: Text('备份结果')),
      body: Column(
        children: [
          Container(
            padding: EdgeInsets.all(16),
            child: Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Column(
                      children: [
                        Text('$successCount', style: TextStyle(fontSize: 24, color: Colors.green)),
                        Text('成功'),
                      ],
                    ),
                    Column(
                      children: [
                        Text('$failedCount', style: TextStyle(fontSize: 24, color: Colors.red)),
                        Text('失败'),
                      ],
                    ),
                    Column(
                      children: [
                        Text('${results.length}', style: TextStyle(fontSize: 24)),
                        Text('总计'),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: results.length,
              itemBuilder: (context, index) {
                final result = results[index];
                return ListTile(
                  leading: Icon(
                    result.success ? Icons.check_circle : Icons.error,
                    color: result.success ? Colors.green : Colors.red,
                  ),
                  title: Text(result.filename),
                  subtitle: result.success 
                    ? Text('备份成功 - ${DateFormat('yyyy-MM-dd HH:mm').format(result.timestamp)}')
                    : Text('备份失败: ${result.error}'),
                  trailing: result.success ? null : IconButton(
                    icon: Icon(Icons.refresh),
                    onPressed: () => _retryBackup(result.filename),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
6. 设置数据持久化
dartclass SettingsRepository {
  static const String _keyImageFolder = 'image_folder';
  static const String _keyLLMBaseUrl = 'llm_base_url';
  static const String _keyLLMModel = 'llm_model';
  static const String _keySystemPrompt = 'system_prompt';
  static const String _keyBackupApiUrl = 'backup_api_url';
  
  // 敏感信息使用加密存储
  static const String _keyLLMApiKey = 'llm_api_key';
  static const String _keyBackupApiKey = 'backup_api_key';
  
  final SharedPreferences _prefs;
  final FlutterSecureStorage _secureStorage;
  
  SettingsRepository(this._prefs, this._secureStorage);
  
  // 保存普通设置
  Future<void> saveImageFolder(String path) async {
    await _prefs.setString(_keyImageFolder, path);
  }
  
  Future<void> saveLLMSettings({
    required String baseUrl,
    required String apiKey,
    required String model,
    required String systemPrompt,
  }) async {
    await _prefs.setString(_keyLLMBaseUrl, baseUrl);
    await _prefs.setString(_keyLLMModel, model);
    await _prefs.setString(_keySystemPrompt, systemPrompt);
    await _secureStorage.write(key: _keyLLMApiKey, value: apiKey);
  }
  
  Future<void> saveBackupSettings({
    required String apiUrl,
    required String apiKey,
  }) async {
    await _prefs.setString(_keyBackupApiUrl, apiUrl);
    await _secureStorage.write(key: _keyBackupApiKey, value: apiKey);
  }
  
  // 读取设置
  Future<AppSettings> loadSettings() async {
    return AppSettings(
      imageFolder: _prefs.getString(_keyImageFolder),
      llmBaseUrl: _prefs.getString(_keyLLMBaseUrl),
      llmApiKey: await _secureStorage.read(key: _keyLLMApiKey),
      llmModel: _prefs.getString(_keyLLMModel),
      systemPrompt: _prefs.getString(_keySystemPrompt) ?? _getDefaultSystemPrompt(),
      backupApiUrl: _prefs.getString(_keyBackupApiUrl),
      backupApiKey: await _secureStorage.read(key: _keyBackupApiKey),
    );
  }
  
  String _getDefaultSystemPrompt() {
    return """你是一个专业的手写文字识别助手。请分析图片中的手写内容：

1. 准确识别所有手写文字内容
2. 保持原文的段落结构和逻辑顺序
3. 输出格式必须是标准Markdown格式
4. 如果有标题，请用适当的#标记层级
5. 如果有列表，请用-或数字标记
6. 忽略涂改、装饰性内容和无关标记
7. 如果内容不清晰，请用[不清晰]标注

请直接输出转换结果，不要添加其他说明文字。""";
  }
}
7. 主界面集成备份功能
dart// 在结果页面添加备份按钮
class ResultsPage extends StatefulWidget {
  final List<ProcessResult> results;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('处理结果'),
        actions: [
          IconButton(
            icon: Icon(Icons.cloud_upload),
            onPressed: _showBackupDialog,
          ),
        ],
      ),
      body: ListView.builder(
        itemCount: results.length,
        itemBuilder: (context, index) {
          final result = results[index];
          return Card(
            child: ListTile(
              title: Text(result.title),
              subtitle: Text('${result.filePath}'),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Icon(Icons.preview),
                    onPressed: () => _previewMarkdown(result),
                  ),
                  IconButton(
                    icon: Icon(Icons.cloud_upload),
                    onPressed: () => _backupSingleFile(result),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
  
  void _showBackupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('备份到云端'),
        content: Text('确定要将所有文件备份到云端吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _backupAllFiles();
            },
            child: Text('备份'),
          ),
        ],
      ),
    );
  }
}
这个更新版本包含了完整的设置管理和云端备份功能，让用户可以：

灵活配置LLM服务：支持不同的API提供商和模型
自定义系统提示词：根据需要调整识别效果
安全的API密钥管理：使用加密存储保护敏感信息
便捷的云端备份：一键备份生成的文档
详细的备份状态跟踪：显示备份成功/失败状态