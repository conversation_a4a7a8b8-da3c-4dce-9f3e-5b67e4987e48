class AppSettings {
  final String? markdownFolder;
  final String? watchFolder;
  final String? llmBaseUrl;
  final String? llmApiKey;
  final String? llmModel;
  final String? systemPrompt;
  final String? backupApiUrl;
  final String? backupApiKey;

  AppSettings({
    this.markdownFolder,
    this.watchFolder,
    this.llmBaseUrl,
    this.llmApiKey,
    this.llmModel,
    this.systemPrompt,
    this.backupApiUrl,
    this.backupApiKey,
  });

  AppSettings copyWith({
    String? markdownFolder,
    String? watchFolder,
    String? llmBaseUrl,
    String? llmApiKey,
    String? llmModel,
    String? systemPrompt,
    String? backupApiUrl,
    String? backupApiKey,
  }) {
    return AppSettings(
      markdownFolder: markdownFolder ?? this.markdownFolder,
      watchFolder: watchFolder ?? this.watchFolder,
      llmBaseUrl: llmBaseUrl ?? this.llmBaseUrl,
      llmApiKey: llmApiKey ?? this.llm<PERSON><PERSON><PERSON><PERSON>,
      llmModel: llmModel ?? this.llmModel,
      systemPrompt: systemPrompt ?? this.systemPrompt,
      backupApiUrl: backupApiUrl ?? this.backupApiUrl,
      backupApiKey: backupApiKey ?? this.backupApiKey,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'markdownFolder': markdownFolder,
      'watchFolder': watchFolder,
      'llmBaseUrl': llmBaseUrl,
      'llmApiKey': llmApiKey,
      'llmModel': llmModel,
      'systemPrompt': systemPrompt,
      'backupApiUrl': backupApiUrl,
      'backupApiKey': backupApiKey,
    };
  }

  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      markdownFolder: json['markdownFolder'],
      watchFolder: json['watchFolder'],
      llmBaseUrl: json['llmBaseUrl'],
      llmApiKey: json['llmApiKey'],
      llmModel: json['llmModel'],
      systemPrompt: json['systemPrompt'],
      backupApiUrl: json['backupApiUrl'],
      backupApiKey: json['backupApiKey'],
    );
  }

  bool get isLLMConfigured {
    return llmBaseUrl != null &&
        llmBaseUrl!.isNotEmpty &&
        llmApiKey != null &&
        llmApiKey!.isNotEmpty &&
        llmModel != null &&
        llmModel!.isNotEmpty;
  }

  bool get isBackupConfigured {
    return backupApiUrl != null &&
        backupApiUrl!.isNotEmpty &&
        backupApiKey != null &&
        backupApiKey!.isNotEmpty;
  }
}
