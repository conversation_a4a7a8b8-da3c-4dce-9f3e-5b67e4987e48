import 'dart:io';
import 'package:flutter/material.dart';
import '../models/article_group.dart';
import '../models/app_settings.dart';
import '../models/process_result.dart';
import '../services/batch_process_service.dart';

class BatchProcessPage extends StatefulWidget {
  final List<File> imageFiles;
  final AppSettings settings;

  const BatchProcessPage({
    super.key,
    required this.imageFiles,
    required this.settings,
  });

  @override
  State<BatchProcessPage> createState() => _BatchProcessPageState();
}

class _BatchProcessPageState extends State<BatchProcessPage> {
  late List<ArticleGroup> _articleGroups;
  final BatchProcessService _batchService = BatchProcessService();
  bool _isProcessing = false;
  int _currentProcessing = 0;
  int _totalProcessing = 0;
  String _currentProcessingTitle = '';
  bool _isSelectionMode = false;
  Set<String> _selectedImageIds = <String>{};
  List<ImageItem> _ignoredImages = [];

  @override
  void initState() {
    super.initState();
    _articleGroups = _batchService.createInitialArticleGroups(widget.imageFiles);
  }

  @override
  void dispose() {
    _batchService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isSelectionMode
            ? '已选择 ${_selectedImageIds.length} 张图片'
            : '批量处理预览'),
        centerTitle: true,
        leading: _isSelectionMode
            ? IconButton(
                icon: const Icon(Icons.close),
                onPressed: _exitSelectionMode,
              )
            : null,
        actions: [
          if (_isSelectionMode) ...[
            IconButton(
              icon: const Icon(Icons.select_all),
              onPressed: _selectAll,
              tooltip: '全选',
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: _selectedImageIds.isNotEmpty ? _deleteSelectedImages : null,
              tooltip: '删除选中',
            ),
          ] else ...[
            IconButton(
              icon: const Icon(Icons.delete_outline),
              onPressed: _enterSelectionMode,
              tooltip: '删除图片',
            ),
            if (!_isProcessing)
              TextButton(
                onPressed: _startBatchProcess,
                child: const Text('开始处理'),
              ),
          ],
        ],
      ),
      body: _isProcessing ? _buildProcessingView() : _buildPreviewView(),
    );
  }

  Widget _buildProcessingView() {
    final progress = _totalProcessing > 0 ? _currentProcessing / _totalProcessing : 0.0;
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(value: progress),
            const SizedBox(height: 24),
            Text(
              '正在处理: $_currentProcessingTitle',
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              '进度: $_currentProcessing / $_totalProcessing',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(value: progress),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewView() {
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue.shade700),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '操作提示',
                      style: TextStyle(
                        color: Colors.blue.shade700,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '• 点击图片可放大查看详情\n• 长按图片可拖动到其他文章组\n• 在同一文章组内可调整图片顺序\n• 拖动到底部忽略区域可跳过处理',
                      style: TextStyle(
                        color: Colors.blue.shade600,
                        fontSize: 12,
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: Column(
            children: [
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                  itemCount: _articleGroups.length,
                  itemBuilder: (context, index) => _buildArticleGroupCard(index),
                ),
              ),
              _buildIgnoreArea(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildIgnoreArea() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: DragTarget<Map<String, dynamic>>(
        onAcceptWithDetails: (details) {
          final data = details.data;
          _moveImageToIgnore(
            data['fromGroupIndex'] as int,
            data['fromImageIndex'] as int,
            data['imageItem'] as ImageItem,
          );
        },
        builder: (context, candidateData, rejectedData) {
          final isHighlighted = candidateData.isNotEmpty;

          return AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            height: _ignoredImages.isEmpty ? 80 : 140,
            decoration: BoxDecoration(
              color: isHighlighted
                  ? Colors.red.shade100
                  : Colors.grey.shade50,
              border: Border.all(
                color: isHighlighted
                    ? Colors.red.shade400
                    : Colors.grey.shade300,
                width: isHighlighted ? 2 : 1,
                style: BorderStyle.solid,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: _ignoredImages.isEmpty
                ? _buildEmptyIgnoreArea(isHighlighted)
                : _buildIgnoreAreaWithImages(isHighlighted),
          );
        },
      ),
    );
  }

  Widget _buildEmptyIgnoreArea(bool isHighlighted) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.delete_sweep,
            size: 32,
            color: isHighlighted ? Colors.red.shade600 : Colors.grey.shade400,
          ),
          const SizedBox(height: 8),
          Text(
            '忽略区域',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: isHighlighted ? Colors.red.shade600 : Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '拖动图片到此处忽略处理',
            style: TextStyle(
              fontSize: 12,
              color: isHighlighted ? Colors.red.shade500 : Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIgnoreAreaWithImages(bool isHighlighted) {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.delete_sweep,
                size: 20,
                color: isHighlighted ? Colors.red.shade600 : Colors.grey.shade600,
              ),
              const SizedBox(width: 8),
              Text(
                '忽略区域 (${_ignoredImages.length} 张图片)',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isHighlighted ? Colors.red.shade600 : Colors.grey.shade600,
                ),
              ),
              const Spacer(),
              if (_ignoredImages.isNotEmpty)
                TextButton(
                  onPressed: _clearIgnoredImages,
                  child: const Text('清空', style: TextStyle(fontSize: 12)),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: _ignoredImages.map((imageItem) {
                  return Container(
                    margin: const EdgeInsets.only(right: 8),
                    child: _buildIgnoredImageThumbnail(imageItem),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIgnoredImageThumbnail(ImageItem imageItem) {
    const double imageSize = 60.0;

    return GestureDetector(
      onTap: () => _isSelectionMode
          ? _toggleImageSelection(imageItem.id)
          : _showImagePreview(imageItem),
      onLongPress: () => _moveImageBackToGroup(imageItem),
      child: Container(
        width: imageSize,
        height: imageSize,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: _isSelectionMode && _selectedImageIds.contains(imageItem.id)
              ? Border.all(color: Colors.blue, width: 2)
              : Border.all(color: Colors.grey.shade300, width: 1),
        ),
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(7),
              child: Image.file(
                imageItem.file,
                width: imageSize,
                height: imageSize,
                fit: BoxFit.cover,
              ),
            ),
            // 忽略标识
            Positioned(
              top: 2,
              right: 2,
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.block,
                  color: Colors.white,
                  size: 12,
                ),
              ),
            ),
            // 选择状态
            if (_isSelectionMode)
              Positioned(
                top: 2,
                left: 2,
                child: Container(
                  padding: const EdgeInsets.all(1),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Icon(
                    _selectedImageIds.contains(imageItem.id)
                        ? Icons.check_circle
                        : Icons.radio_button_unchecked,
                    color: _selectedImageIds.contains(imageItem.id)
                        ? Colors.blue
                        : Colors.grey,
                    size: 14,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildArticleGroupCard(int groupIndex) {
    final articleGroup = _articleGroups[groupIndex];

    return Card(
      margin: const EdgeInsets.only(bottom: 20),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: articleGroup.isMultiImage
                        ? Colors.orange.withValues(alpha: 0.1)
                        : Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    articleGroup.isMultiImage ? Icons.collections : Icons.image,
                    color: articleGroup.isMultiImage ? Colors.orange : Colors.blue,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    articleGroup.title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '${articleGroup.images.length} 张图片',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 12,
              runSpacing: 12,
              alignment: WrapAlignment.start,
              children: articleGroup.images.asMap().entries.map((entry) {
                final imageIndex = entry.key;
                final imageItem = entry.value;
                return _buildImageThumbnail(
                  groupIndex,
                  imageIndex,
                  imageItem,
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageThumbnail(int groupIndex, int imageIndex, ImageItem imageItem) {
    const double imageSize = 120.0; // 增大图片尺寸

    return DragTarget<Map<String, dynamic>>(
      onAcceptWithDetails: (details) {
        final data = details.data;
        _moveImageToGroup(
          data['fromGroupIndex'] as int,
          data['fromImageIndex'] as int,
          groupIndex,
          imageIndex,
        );
      },
      builder: (context, candidateData, rejectedData) {
        return LongPressDraggable<Map<String, dynamic>>(
          data: {
            'fromGroupIndex': groupIndex,
            'fromImageIndex': imageIndex,
            'imageItem': imageItem,
          },
          feedback: Material(
            child: Container(
              width: imageSize,
              height: imageSize,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.file(
                  imageItem.file,
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
          childWhenDragging: Container(
            width: imageSize,
            height: imageSize,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.grey.shade300,
            ),
            child: const Icon(Icons.image, color: Colors.grey, size: 40),
          ),
          child: GestureDetector(
            onTap: () => _isSelectionMode
                ? _toggleImageSelection(imageItem.id)
                : _showImagePreview(imageItem),
            child: Container(
              width: imageSize,
              height: imageSize,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: _getImageBorder(imageItem.id, candidateData.isNotEmpty),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(11),
                    child: Image.file(
                      imageItem.file,
                      width: imageSize,
                      height: imageSize,
                      fit: BoxFit.cover,
                    ),
                  ),
                  // 选择模式下显示复选框，否则显示点击提示图标
                  Positioned(
                    top: 6,
                    left: 6,
                    child: _isSelectionMode
                        ? Container(
                            padding: const EdgeInsets.all(2),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.grey.shade300),
                            ),
                            child: Icon(
                              _selectedImageIds.contains(imageItem.id)
                                  ? Icons.check_circle
                                  : Icons.radio_button_unchecked,
                              color: _selectedImageIds.contains(imageItem.id)
                                  ? Colors.blue
                                  : Colors.grey,
                              size: 20,
                            ),
                          )
                        : Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Colors.black.withValues(alpha: 0.6),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Icon(
                              Icons.zoom_in,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                  ),
                  // 序号标签
                  if (_articleGroups[groupIndex].isMultiImage)
                    Positioned(
                      top: 6,
                      right: 6,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.9),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '${imageIndex + 1}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  // 操作提示
                  if (!_isSelectionMode)
                    Positioned(
                      bottom: 6,
                      left: 6,
                      right: 6,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.6),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Text(
                          '长按拖拽',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _moveImageToGroup(int fromGroupIndex, int fromImageIndex, int toGroupIndex, int toImageIndex) {
    setState(() {
      // 获取要移动的图片
      final imageItem = _articleGroups[fromGroupIndex].images[fromImageIndex];

      // 从原文章组中移除
      _articleGroups[fromGroupIndex].images.removeAt(fromImageIndex);

      // 如果原文章组为空，则删除该文章组
      if (_articleGroups[fromGroupIndex].images.isEmpty) {
        _articleGroups.removeAt(fromGroupIndex);
        // 调整目标索引
        if (toGroupIndex > fromGroupIndex) {
          toGroupIndex--;
        }
      }

      // 添加到目标文章组
      if (toGroupIndex < _articleGroups.length) {
        _articleGroups[toGroupIndex].images.insert(toImageIndex, imageItem);
      } else {
        // 创建新的文章组
        _articleGroups.add(ArticleGroup(
          id: 'article_${DateTime.now().millisecondsSinceEpoch}',
          images: [imageItem],
        ));
      }
    });
  }

  // 选择模式相关方法
  void _enterSelectionMode() {
    setState(() {
      _isSelectionMode = true;
      _selectedImageIds.clear();
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedImageIds.clear();
    });
  }

  void _toggleImageSelection(String imageId) {
    setState(() {
      if (_selectedImageIds.contains(imageId)) {
        _selectedImageIds.remove(imageId);
      } else {
        _selectedImageIds.add(imageId);
      }
    });
  }

  void _selectAll() {
    setState(() {
      _selectedImageIds.clear();
      // 选择文章组中的所有图片
      for (final group in _articleGroups) {
        for (final image in group.images) {
          _selectedImageIds.add(image.id);
        }
      }
      // 选择忽略区域中的所有图片
      for (final image in _ignoredImages) {
        _selectedImageIds.add(image.id);
      }
    });
  }

  // 忽略区域相关方法
  void _moveImageToIgnore(int fromGroupIndex, int fromImageIndex, ImageItem imageItem) {
    setState(() {
      // 从原文章组中移除
      _articleGroups[fromGroupIndex].images.removeAt(fromImageIndex);

      // 如果原文章组为空，则删除该文章组
      if (_articleGroups[fromGroupIndex].images.isEmpty) {
        _articleGroups.removeAt(fromGroupIndex);
      }

      // 添加到忽略列表
      _ignoredImages.add(imageItem);
    });
  }

  void _moveImageBackToGroup(ImageItem imageItem) {
    setState(() {
      // 从忽略列表中移除
      _ignoredImages.remove(imageItem);

      // 创建新的文章组
      _articleGroups.add(ArticleGroup(
        id: 'article_${DateTime.now().millisecondsSinceEpoch}',
        images: [imageItem],
      ));
    });
  }

  void _clearIgnoredImages() {
    setState(() {
      _ignoredImages.clear();
    });
  }

  Border _getImageBorder(String imageId, bool isDragTarget) {
    if (_isSelectionMode && _selectedImageIds.contains(imageId)) {
      return Border.all(color: Colors.blue, width: 3);
    } else if (isDragTarget) {
      return Border.all(color: Colors.blue, width: 3);
    } else {
      return Border.all(color: Colors.grey.shade300, width: 1);
    }
  }

  Future<void> _deleteSelectedImages() async {
    if (_selectedImageIds.isEmpty) return;

    final confirmed = await _showDeleteConfirmDialog(_selectedImageIds.length);
    if (!confirmed) return;

    try {
      // 收集要删除的图片文件
      final filesToDelete = <File>[];

      // 从文章组中收集
      for (final group in _articleGroups) {
        for (final image in group.images) {
          if (_selectedImageIds.contains(image.id)) {
            filesToDelete.add(image.file);
          }
        }
      }

      // 从忽略区域中收集
      for (final image in _ignoredImages) {
        if (_selectedImageIds.contains(image.id)) {
          filesToDelete.add(image.file);
        }
      }

      // 删除文件
      for (final file in filesToDelete) {
        if (await file.exists()) {
          await file.delete();
        }
      }

      // 从文章组和忽略区域中移除已删除的图片
      setState(() {
        // 从文章组中移除
        for (int i = _articleGroups.length - 1; i >= 0; i--) {
          _articleGroups[i].images.removeWhere(
            (image) => _selectedImageIds.contains(image.id),
          );

          // 如果文章组为空，则删除该文章组
          if (_articleGroups[i].images.isEmpty) {
            _articleGroups.removeAt(i);
          }
        }

        // 从忽略区域中移除
        _ignoredImages.removeWhere(
          (image) => _selectedImageIds.contains(image.id),
        );

        _selectedImageIds.clear();
        _isSelectionMode = false;
      });

      _showSuccessSnackBar('成功删除 ${filesToDelete.length} 张图片');
    } catch (e) {
      _showErrorSnackBar('删除图片失败: $e');
    }
  }

  Future<bool> _showDeleteConfirmDialog(int count) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除选中的 $count 张图片吗？\n\n此操作不可撤销，图片将从设备中永久删除。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  Future<void> _startBatchProcess() async {
    if (_articleGroups.isEmpty) {
      _showErrorSnackBar('没有文章需要处理');
      return;
    }

    setState(() {
      _isProcessing = true;
      _totalProcessing = _articleGroups.length;
      _currentProcessing = 0;
    });

    try {
      final results = await _batchService.processAllArticleGroups(
        _articleGroups,
        widget.settings,
        (current, total, title) {
          setState(() {
            _currentProcessing = current;
            _currentProcessingTitle = title;
          });
        },
      );

      if (mounted) {
        _showSuccessDialog(results);
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog('批量处理失败', e.toString());
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  void _showSuccessDialog(List<ProcessResult> results) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('处理完成'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('成功处理了 ${results.length} 个文章，已保存到Markdown文件夹。'),
            if (_ignoredImages.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                '忽略了 ${_ignoredImages.length} 张图片，这些图片不会被删除。',
                style: const TextStyle(fontSize: 12, color: Colors.orange),
              ),
            ],
            const SizedBox(height: 16),
            const Text(
              '是否删除本次处理中使用的图片？',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            const Text(
              '删除后可以节省存储空间，但图片将无法恢复。忽略的图片不会被删除。',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // 返回主页面
            },
            child: const Text('保留图片'),
          ),
          ElevatedButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              navigator.pop();
              await _deleteProcessedImages();
              if (mounted) {
                navigator.pop(); // 返回主页面
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('删除图片'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Future<void> _deleteProcessedImages() async {
    try {
      // 收集所有处理过的图片文件（不包括忽略的图片）
      final filesToDelete = <File>[];
      for (final group in _articleGroups) {
        for (final image in group.images) {
          filesToDelete.add(image.file);
        }
      }

      // 删除文件
      int deletedCount = 0;
      for (final file in filesToDelete) {
        if (await file.exists()) {
          await file.delete();
          deletedCount++;
        }
      }

      String message = '成功删除 $deletedCount 张处理过的图片';
      if (_ignoredImages.isNotEmpty) {
        message += '，保留了 ${_ignoredImages.length} 张忽略的图片';
      }

      if (mounted) {
        _showSuccessSnackBar(message);
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('删除图片失败: $e');
      }
    }
  }

  void _showImagePreview(ImageItem imageItem) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.black,
        child: Stack(
          children: [
            // 图片显示区域
            Center(
              child: InteractiveViewer(
                panEnabled: true,
                boundaryMargin: const EdgeInsets.all(20),
                minScale: 0.5,
                maxScale: 4.0,
                child: Image.file(
                  imageItem.file,
                  fit: BoxFit.contain,
                ),
              ),
            ),
            // 关闭按钮
            Positioned(
              top: 40,
              right: 20,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ),
            ),
            // 图片信息
            Positioned(
              bottom: 40,
              left: 20,
              right: 20,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '文件名: ${imageItem.fileName}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '路径: ${imageItem.filePath}',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '提示: 可以缩放和拖拽查看图片详情',
                      style: TextStyle(
                        color: Colors.white60,
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
