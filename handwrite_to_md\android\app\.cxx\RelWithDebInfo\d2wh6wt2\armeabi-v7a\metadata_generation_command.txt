                        -HD:\Tools\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=D:\Tools\android_sdk\ndk\26.3.11579264
-DCMAKE_ANDROID_NDK=D:\Tools\android_sdk\ndk\26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=D:\Tools\android_sdk\ndk\26.3.11579264\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\Tools\android_sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\android\handwritetomd\handwrite_to_md\build\app\intermediates\cxx\RelWithDebInfo\d2wh6wt2\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\android\handwritetomd\handwrite_to_md\build\app\intermediates\cxx\RelWithDebInfo\d2wh6wt2\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BD:\android\handwritetomd\handwrite_to_md\android\app\.cxx\RelWithDebInfo\d2wh6wt2\armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2