import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import '../models/app_settings.dart';
import '../models/process_result.dart';
import '../services/settings_repository.dart';
import '../services/llm_service.dart';
import '../services/file_service.dart';
import '../services/database_service.dart';
import 'settings_page.dart';
import 'results_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  late SettingsRepository _settingsRepository;
  late LLMService _llmService;
  AppSettings? _settings;
  bool _isLoading = false;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    setState(() => _isLoading = true);
    try {
      _settingsRepository = await SettingsRepository.create();
      _llmService = LLMService();
      await _loadSettings();
    } catch (e) {
      _showErrorSnackBar('初始化失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadSettings() async {
    try {
      final settings = await _settingsRepository.loadSettings();
      setState(() => _settings = settings);
    } catch (e) {
      _showErrorSnackBar('加载设置失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('手写识别转Markdown'),
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _viewResults,
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _openSettings,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildStatusCard(),
            const SizedBox(height: 24),
            _buildActionButtons(),
            const SizedBox(height: 24),
            _buildRecentResults(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    final isConfigured = _settings?.isLLMConfigured ?? false;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isConfigured ? Icons.check_circle : Icons.warning,
                  color: isConfigured ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  isConfigured ? 'LLM已配置' : 'LLM未配置',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (!isConfigured)
              const Text(
                '请先在设置中配置LLM服务，才能使用图片识别功能。',
                style: TextStyle(color: Colors.grey),
              ),
            if (isConfigured && _settings != null)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('模型: ${_settings!.llmModel}'),
                  Text('Base URL: ${_settings!.llmBaseUrl}'),
                  if (_settings!.isBackupConfigured)
                    const Text('云端备份: 已配置', style: TextStyle(color: Colors.green))
                  else
                    const Text('云端备份: 未配置', style: TextStyle(color: Colors.grey)),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    final isConfigured = _settings?.isLLMConfigured ?? false;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ElevatedButton.icon(
          onPressed: isConfigured && !_isProcessing ? _pickImageFromCamera : null,
          icon: const Icon(Icons.camera_alt),
          label: const Text('拍照识别'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
        ),
        const SizedBox(height: 12),
        ElevatedButton.icon(
          onPressed: isConfigured && !_isProcessing ? _pickImageFromGallery : null,
          icon: const Icon(Icons.photo_library),
          label: const Text('从相册选择'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
        ),
        const SizedBox(height: 12),
        OutlinedButton.icon(
          onPressed: isConfigured && !_isProcessing ? _pickImageFromFile : null,
          icon: const Icon(Icons.folder_open),
          label: const Text('从文件选择'),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
        ),
        const SizedBox(height: 12),
        OutlinedButton.icon(
          onPressed: isConfigured && !_isProcessing ? _pickMultipleImagesFromFile : null,
          icon: const Icon(Icons.photo_library_outlined),
          label: const Text('选择多张图片'),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
        ),
      ],
    );
  }

  Widget _buildRecentResults() {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    '最近结果',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: _viewResults,
                    child: const Text('查看全部'),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Expanded(
                child: FutureBuilder<List<ProcessResult>>(
                  future: DatabaseService.getAllResults(),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    }
                    
                    if (snapshot.hasError) {
                      return Center(child: Text('加载失败: ${snapshot.error}'));
                    }
                    
                    final results = snapshot.data ?? [];
                    if (results.isEmpty) {
                      return const Center(
                        child: Text(
                          '暂无处理结果\n点击上方按钮开始识别图片',
                          textAlign: TextAlign.center,
                          style: TextStyle(color: Colors.grey),
                        ),
                      );
                    }
                    
                    // 只显示最近的5个结果
                    final recentResults = results.take(5).toList();
                    
                    return ListView.builder(
                      itemCount: recentResults.length,
                      itemBuilder: (context, index) {
                        final result = recentResults[index];
                        return ListTile(
                          leading: const Icon(Icons.description),
                          title: Text(result.title),
                          subtitle: Text(
                            '${result.processedAt.toString().substring(0, 16)} • ${result.filename}',
                          ),
                          trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                          onTap: () => _viewResult(result),
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _pickImageFromCamera() async {
    final picker = ImagePicker();
    final image = await picker.pickImage(source: ImageSource.camera);
    if (image != null) {
      await _processImage(File(image.path));
    }
  }

  Future<void> _pickImageFromGallery() async {
    final picker = ImagePicker();
    final image = await picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      await _processImage(File(image.path));
    }
  }

  Future<void> _pickImageFromFile() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.image,
      allowMultiple: false,
    );

    if (result != null && result.files.single.path != null) {
      await _processImage(File(result.files.single.path!));
    }
  }

  Future<void> _pickMultipleImagesFromFile() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.image,
      allowMultiple: true,
    );

    if (result != null && result.files.isNotEmpty) {
      List<File> imageFiles = [];
      for (final file in result.files) {
        if (file.path != null) {
          imageFiles.add(File(file.path!));
        }
      }

      if (imageFiles.isNotEmpty) {
        await _processImages(imageFiles);
      }
    }
  }

  Future<void> _processImage(File imageFile) async {
    await _processImages([imageFile]);
  }

  Future<void> _processImages(List<File> imageFiles) async {
    if (_settings == null || !_settings!.isLLMConfigured) {
      _showErrorSnackBar('请先配置LLM设置');
      return;
    }

    if (imageFiles.isEmpty) {
      _showErrorSnackBar('没有选择图片');
      return;
    }

    setState(() => _isProcessing = true);

    try {
      // 显示处理对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(imageFiles.length > 1
                  ? '正在识别 ${imageFiles.length} 张图片内容...'
                  : '正在识别图片内容...'),
            ],
          ),
        ),
      );

      // 调用LLM服务处理图片
      final content = imageFiles.length > 1
          ? await _llmService.processImages(imageFiles, _settings!)
          : await _llmService.processImage(imageFiles.first, _settings!);

      // 生成文件名，传递内容以提取标题
      final filename = FileService.generateFilename(null, content: content);

      // 保存Markdown文件
      final folderPath = _settings!.markdownFolder ?? await FileService.getDefaultImageFolder();
      final filePath = await FileService.saveMarkdownFile(content, filename, folderPath);

      // 保存到数据库 - 对于多图片，我们将第一张图片的路径作为主图片路径
      final result = ProcessResult(
        title: _extractTitle(content),
        filePath: filePath,
        imagePath: imageFiles.first.path,
        content: content,
        processedAt: DateTime.now(),
      );

      final id = await DatabaseService.insertResult(result);
      final savedResult = result.copyWith(id: id);

      // 关闭处理对话框
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 显示成功消息
      _showSuccessSnackBar(imageFiles.length > 1
          ? '${imageFiles.length} 张图片识别完成'
          : '图片识别完成');

      // 跳转到结果页面
      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ResultsPage(initialResult: savedResult),
          ),
        );
      }

    } catch (e) {
      // 关闭处理对话框
      if (mounted) {
        Navigator.of(context).pop();
      }
      _showErrorSnackBar('处理失败: $e');
    } finally {
      setState(() => _isProcessing = false);
    }
  }

  String _extractTitle(String content) {
    // 从Markdown内容中提取标题
    final lines = content.split('\n');
    for (final line in lines) {
      final trimmed = line.trim();
      if (trimmed.startsWith('#')) {
        return trimmed.replaceAll('#', '').trim();
      }
      if (trimmed.isNotEmpty && !trimmed.startsWith('```')) {
        // 如果没有标题，使用第一行非空内容
        return trimmed.length > 30 ? '${trimmed.substring(0, 30)}...' : trimmed;
      }
    }
    return '未命名文档';
  }

  Future<void> _openSettings() async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(builder: (context) => const SettingsPage()),
    );

    if (result == true) {
      // 设置已更新，重新加载
      await _loadSettings();
    }
  }

  void _viewResults() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const ResultsPage()),
    );
  }

  void _viewResult(ProcessResult result) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ResultsPage(initialResult: result),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  void dispose() {
    _llmService.dispose();
    super.dispose();
  }
}
