# 手写识别转Markdown应用 - 真实功能使用指南

## 🚀 新功能概述

应用现在支持真实的LLM API调用和云端备份功能！您可以配置真实的API信息来体验完整的手写识别功能。

## 📋 更新内容

### ✅ 新增功能
1. **真实LLM API集成** - 支持OpenAI GPT-4 Vision和Anthropic Claude 3
2. **文件夹选择功能** - 可以选择自定义的图片保存路径
3. **权限管理** - 完整的相机和存储权限处理
4. **真实云端备份** - 可配置真实的备份API服务
5. **增强的结果管理** - 更好的备份状态显示和管理

### 🔧 技术改进
- 完整的权限请求流程
- 真实的文件系统操作
- 错误处理和重试机制
- 网络请求超时和错误处理

## 🔑 LLM API配置

### OpenAI GPT-4 Vision配置

1. **获取API密钥**
   - 访问 [OpenAI Platform](https://platform.openai.com/)
   - 注册账户并获取API密钥

2. **配置信息**
   ```
   Base URL: https://api.openai.com/v1
   API Key: sk-your-actual-api-key-here
   模型: gpt-4-vision-preview 或 gpt-4o
   ```

3. **系统提示词示例**
   ```
   你是一个专业的手写文字识别助手。请分析图片中的手写内容：
   
   1. 准确识别所有手写文字内容
   2. 保持原文的段落结构和逻辑顺序
   3. 输出格式必须是标准Markdown格式
   4. 如果有标题，请用适当的#标记层级
   5. 如果有列表，请用-或数字标记
   6. 忽略涂改、装饰性内容和无关标记
   7. 如果内容不清晰，请用[不清晰]标注
   
   请直接输出转换结果，不要添加其他说明文字。
   ```

### Anthropic Claude 3配置

1. **获取API密钥**
   - 访问 [Anthropic Console](https://console.anthropic.com/)
   - 注册账户并获取API密钥

2. **配置信息**
   ```
   Base URL: https://api.anthropic.com
   API Key: sk-ant-your-actual-api-key-here
   模型: claude-3-sonnet-20240229 或 claude-3-opus-20240229
   ```

### 其他兼容API服务

应用也支持其他兼容OpenAI API格式的服务：
- **Azure OpenAI**: `https://your-resource.openai.azure.com`
- **本地部署**: `http://localhost:8000/v1`
- **第三方服务**: 任何兼容OpenAI API格式的服务

## ☁️ 云端备份配置

### 支持的备份服务

应用支持任何提供REST API的文件存储服务：

1. **自建服务器**
   ```
   API地址: https://your-server.com/api/backup
   API密钥: your-backup-api-key
   ```

2. **云存储服务**
   - 可以配置支持REST API的云存储服务
   - 需要提供上传接口和认证密钥

### API接口要求

备份API需要支持以下请求格式：

```json
POST /api/backup
Content-Type: application/json

{
  "apikey": "your-api-key",
  "action": "create",
  "filename": "Handwrite/filename.md",
  "content": "markdown content here"
}
```

响应格式：
```json
{
  "success": true,
  "message": "File uploaded successfully"
}
```

## 📱 使用流程

### 第一步：配置LLM服务

1. 打开应用，点击"配置设置"
2. 填写LLM配置：
   - **Base URL**: 您的LLM服务地址
   - **API Key**: 您的真实API密钥
   - **模型**: 选择支持图像识别的模型
   - **系统提示词**: 使用默认或自定义
3. 点击"测试连接"验证配置
4. 保存设置

### 第二步：配置文件夹（可选）

1. 在设置页面的"图片文件夹"部分
2. 点击"选择"按钮
3. 选择您希望保存Markdown文件的文件夹
4. 应用会自动请求必要的存储权限

### 第三步：配置云端备份（可选）

1. 在设置页面的"云端备份"部分
2. 填写备份API地址和密钥
3. 点击"测试连接"验证配置
4. 保存设置

### 第四步：开始识别

1. 返回主页，确认显示"LLM已配置"
2. 点击"拍照识别"或"从相册选择"
3. 应用会自动请求相机权限（首次使用）
4. 选择包含手写内容的图片
5. 等待LLM处理（通常需要5-15秒）
6. 查看识别结果并选择保存

### 第五步：管理结果

1. 点击"查看历史结果"
2. 查看所有识别历史
3. 点击任意结果查看详细内容
4. 使用备份功能将文件上传到云端

## 🔧 故障排除

### LLM连接问题

1. **API密钥错误**
   - 检查API密钥是否正确
   - 确认API密钥有足够的额度

2. **网络连接问题**
   - 检查网络连接
   - 确认API服务地址正确

3. **模型不支持**
   - 确认选择的模型支持图像识别
   - 检查API服务是否支持所选模型

### 权限问题

1. **相机权限被拒绝**
   - 在系统设置中手动授予相机权限
   - 重启应用重新请求权限

2. **存储权限被拒绝**
   - 在系统设置中授予存储权限
   - 对于Android 13+，授予照片权限

### 文件保存问题

1. **文件夹不存在**
   - 应用会自动创建文件夹
   - 确认有写入权限

2. **存储空间不足**
   - 清理设备存储空间
   - 选择其他存储位置

## 💡 使用技巧

### 提高识别准确率

1. **图片质量**
   - 确保光线充足
   - 避免阴影和反光
   - 保持图片清晰

2. **手写内容**
   - 字迹尽量工整
   - 避免过于潦草的字体
   - 确保文字对比度足够

3. **拍摄角度**
   - 保持垂直拍摄
   - 避免倾斜和变形
   - 确保文字完整在画面内

### 优化系统提示词

可以根据具体需求自定义系统提示词：

```
针对会议记录：
请识别这份会议记录，重点关注：
1. 会议基本信息（时间、地点、参与人员）
2. 讨论议题和要点
3. 决议和行动项
4. 输出为结构化的Markdown格式

针对学习笔记：
请识别这份学习笔记，注意：
1. 保持知识点的层次结构
2. 识别公式和代码片段
3. 保留重点标记和下划线
4. 输出为易于阅读的Markdown格式
```

## 🔒 安全注意事项

1. **API密钥安全**
   - 不要在公共场所输入API密钥
   - 定期更换API密钥
   - 监控API使用量

2. **数据隐私**
   - 敏感内容谨慎上传
   - 了解LLM服务的数据处理政策
   - 考虑使用本地部署的模型

3. **备份安全**
   - 使用HTTPS协议的备份服务
   - 确保备份服务的安全性
   - 定期检查备份文件

## 📞 技术支持

如果遇到问题，可以：

1. 检查应用日志和错误信息
2. 验证API配置是否正确
3. 测试网络连接和权限设置
4. 查看本文档的故障排除部分

应用现在已经具备完整的生产级功能，可以处理真实的手写识别任务！
