{"buildFiles": ["D:\\Tools\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Tools\\android_sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\android\\handwritetomd\\handwrite_to_md\\android\\app\\.cxx\\Debug\\8465l5e6\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["D:\\Tools\\android_sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\android\\handwritetomd\\handwrite_to_md\\android\\app\\.cxx\\Debug\\8465l5e6\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}