{"buildFiles": ["D:\\Tools\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Tools\\android_sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\android\\handwritetomd\\handwrite_to_md\\android\\app\\.cxx\\RelWithDebInfo\\d2wh6wt2\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\Tools\\android_sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\android\\handwritetomd\\handwrite_to_md\\android\\app\\.cxx\\RelWithDebInfo\\d2wh6wt2\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\Tools\\android_sdk\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\Tools\\android_sdk\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}