import 'dart:io';
import 'dart:convert';
import 'package:path/path.dart' as path;
import 'package:permission_handler/permission_handler.dart';
import '../models/markdown_file.dart';

class FileService {
  static Future<bool> requestStoragePermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.request();
      if (status != PermissionStatus.granted) {
        // 对于Android 13+，尝试请求照片权限
        final photoStatus = await Permission.photos.request();
        return photoStatus == PermissionStatus.granted;
      }
      return true;
    }
    return true; // iOS不需要特殊权限
  }

  static Future<String> getDefaultImageFolder() async {
    if (Platform.isAndroid) {
      return '/storage/emulated/0/HandwriteApp/';
    } else if (Platform.isIOS) {
      final directory = Directory.systemTemp;
      return '${directory.path}/HandwriteApp/';
    }
    return './HandwriteApp/';
  }

  static Future<bool> createDirectoryIfNotExists(String dirPath) async {
    try {
      final directory = Directory(dirPath);
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }
      return true;
    } catch (e) {
      return false;
    }
  }

  static Future<String> saveMarkdownFile(
    String content,
    String filename,
    String folderPath,
  ) async {
    try {
      // 确保目录存在
      await createDirectoryIfNotExists(folderPath);
      
      // 确保文件名以.md结尾
      if (!filename.endsWith('.md')) {
        filename = '$filename.md';
      }
      
      final filePath = path.join(folderPath, filename);
      final file = File(filePath);
      
      // 如果文件已存在，添加时间戳避免覆盖
      if (await file.exists()) {
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final nameWithoutExt = path.basenameWithoutExtension(filename);
        final ext = path.extension(filename);
        filename = '${nameWithoutExt}_$timestamp$ext';
        final newFilePath = path.join(folderPath, filename);
        await File(newFilePath).writeAsString(content, encoding: utf8);
        return newFilePath;
      } else {
        await file.writeAsString(content, encoding: utf8);
        return filePath;
      }
    } catch (e) {
      throw Exception('保存文件失败: $e');
    }
  }

  static Future<String?> readMarkdownFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.readAsString(encoding: utf8);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  static Future<bool> deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  // 批量删除文件
  static Future<Map<String, bool>> deleteFiles(List<String> filePaths) async {
    Map<String, bool> results = {};

    for (String filePath in filePaths) {
      try {
        final file = File(filePath);
        if (await file.exists()) {
          await file.delete();
          results[filePath] = true;
        } else {
          results[filePath] = false; // 文件不存在
        }
      } catch (e) {
        results[filePath] = false; // 删除失败
      }
    }

    return results;
  }

  static Future<List<MarkdownFile>> getMarkdownFilesFromDirectory(String dirPath) async {
    try {
      final directory = Directory(dirPath);
      if (!await directory.exists()) {
        return [];
      }

      final files = await directory
          .list()
          .where((entity) => entity is File && entity.path.endsWith('.md'))
          .cast<File>()
          .toList();

      List<MarkdownFile> markdownFiles = [];
      for (final file in files) {
        try {
          final content = await file.readAsString(encoding: utf8);
          final stat = await file.stat();
          final filename = path.basename(file.path);
          
          markdownFiles.add(MarkdownFile(
            filename: filename,
            content: content,
            filePath: file.path,
            createdAt: stat.modified,
          ));
        } catch (e) {
          // 跳过无法读取的文件
          continue;
        }
      }

      // 按修改时间排序
      markdownFiles.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      return markdownFiles;
    } catch (e) {
      return [];
    }
  }

  static String generateFilename(String? customName, {String? content}) {
    final now = DateTime.now();
    final timestamp = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}';

    if (customName != null && customName.isNotEmpty) {
      // 清理文件名，移除非法字符
      final cleanName = customName.replaceAll(RegExp(r'[<>:"/\\|?*]'), '_');
      return '${cleanName}_$timestamp';
    } else if (content != null && content.isNotEmpty) {
      // 从markdown内容中提取标题
      final title = _extractTitleFromContent(content);
      if (title.isNotEmpty) {
        // 去除所有标点符号，只保留纯文本（中文、英文、数字）
        final cleanTitle = title.replaceAll(RegExp(r'[^\w\u4e00-\u9fa5]'), '');
        // 取前10个字符（按字符数计算，不是字节数）
        final shortTitle = _getFirstNCharacters(cleanTitle, 10);
        if (shortTitle.isNotEmpty) {
          return '${shortTitle}_$timestamp';
        }
      }
      return 'handwrite_$timestamp';
    } else {
      return 'handwrite_$timestamp';
    }
  }

  // 获取字符串的前N个字符（正确处理Unicode字符）
  static String _getFirstNCharacters(String text, int n) {
    if (text.length <= n) {
      return text;
    }
    return text.substring(0, n);
  }

  // 从markdown内容中提取标题的辅助方法
  static String _extractTitleFromContent(String content) {
    final lines = content.split('\n');
    bool inCodeBlock = false;

    for (final line in lines) {
      final trimmed = line.trim();

      // 检查代码块开始/结束
      if (trimmed.startsWith('```')) {
        inCodeBlock = !inCodeBlock;
        continue;
      }

      // 跳过代码块内的内容
      if (inCodeBlock) {
        continue;
      }

      // 检查标题
      if (trimmed.startsWith('# ')) {
        return trimmed.substring(2).trim();
      }
      if (trimmed.startsWith('## ')) {
        return trimmed.substring(3).trim();
      }
      if (trimmed.startsWith('### ')) {
        return trimmed.substring(4).trim();
      }

      // 如果没有找到标题，使用第一行非空内容（但不是代码块或其他特殊格式）
      if (trimmed.isNotEmpty && !trimmed.startsWith('#')) {
        return trimmed;
      }
    }
    return '';
  }

  static Future<bool> isFileExists(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  static Future<int> getFileSize(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        final stat = await file.stat();
        return stat.size;
      }
      return 0;
    } catch (e) {
      return 0;
    }
  }

  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  static Future<List<File>> getImageFilesFromDirectory(String dirPath) async {
    try {
      final directory = Directory(dirPath);
      if (!await directory.exists()) {
        return [];
      }

      final imageExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp'];
      final files = await directory
          .list()
          .where((entity) => entity is File)
          .cast<File>()
          .where((file) => imageExtensions.any((ext) =>
              file.path.toLowerCase().endsWith(ext)))
          .toList();

      // 按修改时间排序
      files.sort((a, b) {
        try {
          final aStat = a.statSync();
          final bStat = b.statSync();
          return bStat.modified.compareTo(aStat.modified);
        } catch (e) {
          return 0;
        }
      });

      return files;
    } catch (e) {
      return [];
    }
  }

  // 检查路径是否为content URI
  static bool isContentUri(String path) {
    return path.startsWith('content://');
  }

  // 尝试将content URI转换为文件系统路径
  static String? convertContentUriToPath(String contentUri) {
    if (!isContentUri(contentUri)) {
      return contentUri;
    }

    // 尝试从content URI中提取实际路径
    // content://com.android.externalstorage.documents/tree/primary%3ADownload%2Fhuanimage
    if (contentUri.contains('primary%3A')) {
      final parts = contentUri.split('primary%3A');
      if (parts.length > 1) {
        final pathPart = Uri.decodeComponent(parts[1]);
        return '/storage/emulated/0/$pathPart';
      }
    }

    return null;
  }

  // 添加一个调试版本的方法
  static Future<Map<String, dynamic>> getImageFilesFromDirectoryWithDebug(String dirPath) async {
    try {
      String actualPath = dirPath;
      String debugInfo = '原始路径: $dirPath\n';

      // 检查是否为content URI
      if (isContentUri(dirPath)) {
        debugInfo += '检测到content URI\n';
        final convertedPath = convertContentUriToPath(dirPath);
        if (convertedPath != null) {
          actualPath = convertedPath;
          debugInfo += '转换后路径: $actualPath\n';
        } else {
          return {
            'files': <File>[],
            'error': '无法处理content URI: $dirPath\n请手动输入文件系统路径，如: /storage/emulated/0/Download/images',
            'debug': '$debugInfo无法转换content URI'
          };
        }
      }

      // 检查路径权限
      final permissionCheck = await _checkDirectoryPermissions(actualPath);
      if (!permissionCheck['hasPermission']) {
        return {
          'files': <File>[],
          'error': permissionCheck['error'],
          'debug': '$debugInfo权限检查失败: ${permissionCheck['details']}'
        };
      }

      final directory = Directory(actualPath);
      if (!await directory.exists()) {
        return {
          'files': <File>[],
          'error': '目录不存在: $actualPath\n请检查路径是否正确',
          'debug': '${debugInfo}Directory does not exist'
        };
      }

      final imageExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp'];

      // 使用更安全的方式获取文件列表
      List<FileSystemEntity> allEntities = [];
      List<File> allFiles = [];

      try {
        allEntities = await directory.list().toList();
        allFiles = allEntities.whereType<File>().toList();
      } catch (e) {
        // 如果直接列举失败，尝试使用同步方式
        try {
          allEntities = directory.listSync();
          allFiles = allEntities.whereType<File>().toList();
        } catch (syncError) {
          return {
            'files': <File>[],
            'error': '无法访问目录内容: $actualPath\n错误: $e\n\n这可能是权限问题，请尝试：\n1. 选择其他目录\n2. 检查应用权限设置\n3. 使用公共目录如 /storage/emulated/0/Download',
            'debug': '$debugInfo列举文件失败: $e\n同步方式也失败: $syncError'
          };
        }
      }

      // 过滤出图片文件
      final imageFiles = allFiles.where((file) =>
          imageExtensions.any((ext) => file.path.toLowerCase().endsWith(ext))
      ).toList();

      // 按修改时间排序
      imageFiles.sort((a, b) {
        try {
          final aStat = a.statSync();
          final bStat = b.statSync();
          return bStat.modified.compareTo(aStat.modified);
        } catch (e) {
          return 0;
        }
      });

      return {
        'files': imageFiles,
        'debug': '$debugInfo实际扫描目录: $actualPath\n总项目: ${allEntities.length}\n文件: ${allFiles.length}\n图片: ${imageFiles.length}',
        'allFileNames': allFiles.map((f) => f.path.split('/').last).toList(),
        'imageFileNames': imageFiles.map((f) => f.path.split('/').last).toList(),
      };
    } catch (e) {
      String errorMessage = '扫描目录时出错: $e';
      String suggestions = '';

      if (e.toString().contains('Permission denied')) {
        suggestions = '\n\n建议解决方案：\n'
            '1. 授予应用"管理所有文件"权限\n'
            '2. 选择公共目录如：\n'
            '   • /storage/emulated/0/Download\n'
            '   • /storage/emulated/0/Pictures\n'
            '   • /storage/emulated/0/DCIM\n'
            '3. 避免使用中文路径名\n'
            '4. 检查目录是否真实存在';
      }

      return {
        'files': <File>[],
        'error': errorMessage + suggestions,
        'debug': 'Exception: $e'
      };
    }
  }

  // 检查目录权限的辅助方法
  static Future<Map<String, dynamic>> _checkDirectoryPermissions(String dirPath) async {
    try {
      final directory = Directory(dirPath);

      // 检查目录是否存在
      if (!await directory.exists()) {
        return {
          'hasPermission': false,
          'error': '目录不存在: $dirPath',
          'details': 'Directory does not exist'
        };
      }

      // 尝试读取目录权限
      try {
        await directory.stat();
        // 如果能获取stat信息，说明有基本访问权限
      } catch (e) {
        return {
          'hasPermission': false,
          'error': '无法访问目录: $dirPath\n权限不足',
          'details': 'Cannot stat directory: $e'
        };
      }

      // 尝试列举目录（只获取第一个项目来测试权限）
      try {
        await directory.list().take(1).toList();
      } catch (e) {
        if (e.toString().contains('Permission denied')) {
          return {
            'hasPermission': false,
            'error': '目录访问被拒绝: $dirPath\n请检查应用权限设置',
            'details': 'Permission denied when listing directory: $e'
          };
        }
        // 其他错误可能不是权限问题
      }

      return {
        'hasPermission': true,
        'error': null,
        'details': 'Permission check passed'
      };
    } catch (e) {
      return {
        'hasPermission': false,
        'error': '权限检查失败: $e',
        'details': 'Permission check exception: $e'
      };
    }
  }
}
